# Accounting & Expenses Management Training Video Script
## Complete User Guide for ERP Accounting & Financial Management System

### Table of Contents
1. [Introduction](#introduction)
2. [System Overview](#system-overview)
3. [Getting Started](#getting-started)
4. [Chart of Accounts](#chart-of-accounts)
5. [Core Features](#core-features)
6. [Advanced Workflows](#advanced-workflows)
7. [Financial Reports & Analytics](#financial-reports--analytics)
8. [Import & Export Operations](#import--export-operations)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

---

## Introduction

Welcome to the comprehensive training guide for our ERP Accounting and Expenses Management System. This guide will walk you through every aspect of financial management, from basic account setup to advanced financial reporting and analysis.

**What You'll Learn:**
- How to set up and manage chart of accounts effectively
- Understanding expense recording and approval workflows
- Advanced features like journal entries, reconciliation, and cost centers
- Financial reporting including trial balance, P&L, and balance sheet
- Integration with other system modules (invoices, bills, inventory)
- Best practices for financial accuracy and compliance

---

## System Overview

### Architecture Overview

```mermaid
graph TD
    A[Chart of Accounts] --> B[Account Configuration]
    B --> C[Transaction Recording]
    C --> D[Journal Entries]
    D --> E[Financial Reports]
    E --> F[Analytics & KPIs]

    B --> G[Expense Management]
    C --> H[Leg Management]
    D --> I[Posting & Reconciliation]
    E --> J[Trial Balance]
    F --> K[Dashboard Widgets]
    G --> L[Approval Workflows]
```

### Key Components

1. **Chart of Accounts**: Hierarchical account structure with categories
2. **Journal Entries (Legs)**: Individual debit/credit entries
3. **Expense Records**: Expense tracking and approval workflows
4. **Ledger Entries**: Manual journal entry transactions
5. **Financial Reports**: Trial balance, P&L, balance sheet, cash flow
6. **Cost Centers**: Department and project-based accounting

---

## Getting Started

### Accessing the Accounting Module

**Navigation Path:**
```
Dashboard → المحاسبة → قائمة الحسابات → List Accounts
```

### Initial Setup

1. **User Permissions Required:**
   - `view_account` - Basic viewing access
   - `add_account` - Create new accounts
   - `change_account` - Edit existing accounts
   - `delete_account` - Remove accounts
   - `general_ledger` - View general ledger
   - `export_accounts` - Export account data
   - `view_expenserecord` - View expense records
   - `add_expenserecord` - Create expense records
   - `post_expense_record` - Post expenses to accounting

2. **Default Settings:**
   - Default currency (configurable per tenant)
   - Default accounts (income, COGS, inventory assets)
   - Cost center assignments
   - Approval workflows

---

## Chart of Accounts

### Account Structure

```mermaid
stateDiagram-v2
    [*] --> Assets: الأصول
    [*] --> Liabilities: الإلتزامات
    [*] --> Equity: حقوق الملكية
    [*] --> Income: الإيرادات
    [*] --> COGS: تكلفة البضاعة المباعة
    [*] --> Expenses: النفقات

    Assets --> CurrentAssets: الأصول المتداولة
    Assets --> FixedAssets: الأصول الثابتة
    Liabilities --> CurrentLiabilities: الإلتزامات المتداولة
    Liabilities --> LongTermLiabilities: الإلتزامات طويلة الأجل
```

### Account Categories

| Category | Description | Statement Type | Cash Flow |
|----------|-------------|----------------|-----------|
| الأصول (Assets) | Company resources | قائمة المركز المالي | Operating/Investing |
| الإلتزامات (Liabilities) | Company obligations | قائمة المركز المالي | Operating/Financing |
| حقوق الملكية (Equity) | Owner's equity | قائمة المركز المالي | Financing |
| الإيرادات (Income) | Revenue accounts | قائمة الدخل | Operating |
| تكلفة البضاعة المباعة (COGS) | Cost of goods sold | قائمة الدخل | Operating |
| النفقات (Expenses) | Operating expenses | قائمة الدخل | Operating |

### Account Coding System

**Account Code Structure:**
```python
# Account coding format:
Parent Code + Account Code = Full Code
Example:
- Assets (1000)
  - Current Assets (1100)
    - Cash (1101)
    - Accounts Receivable (1102)
```

**Code Generation Rules:**
- Root accounts: 4-digit codes (1000, 2000, etc.)
- Sub-accounts: Parent code + 2-digit extension
- Leaf accounts: Final level for transaction posting
- Immutable accounts: System-protected accounts

---

## Core Features

### 1. Account Management

#### Creating Accounts
```python
# Navigate to: المحاسبة → قائمة الحسابات → Add Account
# Required fields:
- Name (اسم الحساب)
- Code (ترميز الحساب)
- Parent Account (الحساب الأب)
- Type (القائمة): Balance Sheet or Income Statement
- Category (الفئة): Assets, Liabilities, etc.
- Cash Flow (طبيعة الحساب في قائمة التدفقات)
```

#### Account Configuration
**Basic Information:**
- **Name**: Descriptive account name
- **Code**: Unique numeric identifier
- **Parent**: Hierarchical relationship
- **Type**: Statement classification
- **Category**: Account grouping

**Advanced Settings:**
- **Immutable**: Prevents modification
- **Cash Flow**: Cash flow statement classification
- **Full Code**: Auto-generated complete code

### 2. Expense Record Management

#### Creating Expense Records
```
1. Navigate to: المحاسبة → سندات الدفع → Add Expense Record
2. Fill basic information:
   - Date (التاريخ)
   - Reference (المرجع)
   - Payment Method (طريقة الدفع)
   - Memo (ملاحظة)
3. Add journal entries (legs)
4. Post to accounting
```

#### Expense Workflow
```mermaid
flowchart LR
    A[Create Expense] --> B[Add Details]
    B --> C[Add Journal Entries]
    C --> D[Review & Validate]
    D --> E[Post to Accounting]
    E --> F[Generate Reports]
```

### 3. Journal Entry Management

#### Creating Journal Entries (Legs)
1. **Debit Entries**: Increase assets/expenses, decrease liabilities/income
2. **Credit Entries**: Increase liabilities/income, decrease assets/expenses
3. **Balance Validation**: Total debits must equal total credits
4. **Account Selection**: Choose appropriate chart of accounts

#### Leg Properties
```
Leg Components:
- Account: Chart of accounts selection
- Debit Amount: Debit value in specified currency
- Credit Amount: Credit value in specified currency
- Currency: Transaction currency
- Memo: Description/reference
- Cost Center: Department/project assignment
```

### 4. Transaction Types

#### Supported Transaction Types
- **EXPENSE_RECORD**: General expense vouchers
- **LEDGER_ENTRY**: Manual journal entries
- **INVENTORY_EXPENSE_RECORD**: Inventory-related expenses
- **INVOICE**: Sales transactions (auto-generated)
- **BILL**: Purchase transactions (auto-generated)
- **PAYMENT**: Payment transactions (auto-generated)

#### Transaction Status
```
Transaction Lifecycle:
- Draft: Initial creation
- Posted: Committed to accounting
- Canceled: Voided transaction
- Reversed: Accounting reversal
```

---

## Advanced Workflows

### 1. Cost Center Management

#### Setting Up Cost Centers
```mermaid
flowchart TD
    A[Create Cost Center] --> B[Assign to Accounts]
    B --> C[Allocate Transactions]
    C --> D[Generate Reports]
    D --> E[Analyze Performance]
```

#### Cost Center Applications
- **Department Tracking**: Track expenses by department
- **Project Accounting**: Monitor project costs and revenues
- **Profit Center Analysis**: Evaluate profitability by division
- **Budget Control**: Compare actual vs. budgeted amounts

### 2. Account Reconciliation

#### Reconciliation Process
1. **Generate Trial Balance**: Review account balances
2. **Compare External Sources**: Bank statements, vendor statements
3. **Identify Discrepancies**: Find unmatched transactions
4. **Create Adjusting Entries**: Correct differences
5. **Finalize Reconciliation**: Document completion

### 3. Multi-Currency Handling

#### Currency Features
- **Multi-currency transactions**: Support for multiple currencies
- **Exchange rate management**: Automatic rate conversion
- **Currency revaluation**: Period-end adjustments
- **Reporting currency**: Consolidated reporting in base currency

### 4. Approval Workflows

#### Expense Approval Process
```
Approval Hierarchy:
1. Employee submits expense
2. Department manager review
3. Finance department validation
4. Final approval and posting
5. Payment processing
```

---

## Financial Reports & Analytics

### 1. Core Financial Reports

#### Trial Balance Report
```mermaid
graph LR
    A[Account Balances] --> B[Debit Totals]
    A --> C[Credit Totals]
    B --> D[Balance Verification]
    C --> D
    D --> E[Financial Statements]
```

**Trial Balance Features:**
- **Summary View**: Account balances only
- **Detail View**: Transaction-level detail
- **Date Range Filtering**: Specific period analysis
- **Currency Selection**: Multi-currency reporting
- **Zero Balance Options**: Include/exclude zero balances

#### Profit & Loss Statement
**P&L Components:**
- **Revenue**: Income account totals
- **Cost of Goods Sold**: Direct costs
- **Gross Profit**: Revenue minus COGS
- **Operating Expenses**: Administrative and selling expenses
- **Net Income**: Final profit/loss calculation

#### Balance Sheet
**Balance Sheet Sections:**
- **Assets**: Current and fixed assets
- **Liabilities**: Current and long-term liabilities
- **Equity**: Owner's equity and retained earnings
- **Balance Verification**: Assets = Liabilities + Equity

### 2. Analytical Reports

#### Available Analytics
1. **Profit & Loss by Cost Center**: Departmental profitability
2. **Profit & Loss by Customer**: Customer profitability analysis
3. **Income by Inventory**: Warehouse performance
4. **Income by Province**: Geographic analysis
5. **Receivables & Payables**: Cash flow analysis

#### Report Filtering Options
```
Filter Criteria:
- Date ranges (from/to dates)
- Currency selection
- Cost center filtering
- Inventory location
- Customer/supplier filtering
- Account category filtering
```

### 3. Dashboard Analytics

#### Key Performance Indicators
- **Total Assets**: Company asset valuation
- **Total Liabilities**: Outstanding obligations
- **Net Worth**: Equity calculation
- **Monthly Revenue**: Revenue trends
- **Expense Ratios**: Cost analysis metrics

#### Visual Analytics
- Account balance trends over time
- Expense category distribution
- Revenue vs. expense comparison
- Cash flow projections
- Budget variance analysis

---

## Integration Features

### 1. Sales Integration

**Invoice Integration:**
- Automatic journal entries on invoice posting
- Revenue recognition in income accounts
- Accounts receivable updates
- Tax liability recording

### 2. Purchase Integration

**Bill Integration:**
- Expense recording on bill posting
- Accounts payable updates
- Inventory asset adjustments
- Cost allocation to projects

### 3. Inventory Integration

**Stock Integration:**
- Inventory valuation updates
- Cost of goods sold calculations
- Asset account adjustments
- Variance analysis

### 4. Payment Integration

**Payment Processing:**
- Cash account updates
- Receivable/payable reductions
- Bank reconciliation support
- Multi-currency handling

---

## Import & Export Operations

### 1. Account Import Process

#### Supported Import Types
```
Import Categories:
- legledgerentry: Ledger entry transactions
- legdebit: Debit leg entries
- legcredit: Credit leg entries
```

#### Import File Format
**Excel Template Structure:**
```
Row 1: Field Types (metadata)
Row 2: Field Names (headers)
Row 3+: Data rows
```

**Required Fields for Ledger Import:**
- `account`: Account name or code
- `debit`: Debit amount
- `credit`: Credit amount
- `currency`: Currency code
- `memo`: Transaction description

### 2. Export Operations

#### Export Formats
- **Excel**: Full data export with formatting
- **CSV**: Comma-separated values
- **PDF**: Formatted financial reports

#### Export Options
```
Export Filters:
- Date range selection
- Account filtering
- Transaction type filtering
- Currency selection
- Posted/unposted status
```

---

## Best Practices

### 1. Account Structure

**Design Guidelines:**
- [ ] Logical account hierarchy
- [ ] Consistent naming conventions
- [ ] Appropriate account codes
- [ ] Clear category assignments
- [ ] Proper parent-child relationships

### 2. Transaction Recording

**Recording Standards:**
- Use descriptive memos for all entries
- Ensure balanced journal entries
- Assign appropriate cost centers
- Maintain supporting documentation
- Review before posting

### 3. Financial Controls

**Control Measures:**
- Regular account reconciliations
- Monthly financial statement preparation
- Budget vs. actual analysis
- Approval workflow enforcement
- Audit trail maintenance

### 4. Reporting Accuracy

**Quality Assurance:**
- Verify trial balance balancing
- Cross-check with external sources
- Review unusual account balances
- Validate currency conversions
- Document adjusting entries

---

## Troubleshooting

### Common Issues and Solutions

#### Issue 1: Trial Balance Not Balancing
**Symptoms:** Total debits ≠ total credits
**Solutions:**
- Review all unposted transactions
- Check for incomplete journal entries
- Verify currency conversion rates
- Look for deleted or modified entries
- Run account balance reconciliation

#### Issue 2: Account Cannot Be Deleted
**Symptoms:** Error when attempting account deletion
**Solutions:**
- Check for existing transactions in account
- Verify no child accounts exist
- Ensure account is not marked as immutable
- Review system default account assignments

#### Issue 3: Expense Record Won't Post
**Symptoms:** Posting fails with error message
**Solutions:**
- Verify all required fields completed
- Check journal entry balance
- Ensure valid account selections
- Verify user posting permissions
- Review currency and amount formats

#### Issue 4: Financial Reports Show Incorrect Balances
**Symptoms:** Report balances don't match expectations
**Solutions:**
- Verify date range settings
- Check currency selection
- Review account filtering options
- Ensure all transactions are posted
- Validate account categorization

### Getting Help

**Support Resources:**
- User manual (this guide)
- Video tutorials
- Help desk: <EMAIL>
- Training sessions: Monthly webinars

---

## Quick Reference

### Keyboard Shortcuts

| Action | Shortcut |
|--------|----------|
| New Account | Ctrl+N |
| Save Entry | Ctrl+S |
| Search Accounts | Ctrl+F |
| Post Transaction | Ctrl+P |
| Generate Report | Ctrl+R |

### Common Tasks

| Task | Navigation Path |
|------|-----------------|
| View chart of accounts | المحاسبة → قائمة الحسابات |
| Create expense record | المحاسبة → سندات الدفع → Add |
| Generate trial balance | التقارير → المحاسبة → ميزان المراجعة |
| View general ledger | الحساب → General Ledger |
| Create journal entry | المحاسبة → سندات القيود → Add |

---

## Conclusion

This comprehensive guide covers all aspects of the accounting and expenses management system. Regular practice with these features will ensure accurate financial management and reporting.

**Next Steps:**
1. Set up chart of accounts structure
2. Practice creating expense records
3. Generate trial balance reports
4. Schedule regular financial reviews

**Remember:** The key to mastering the accounting system is consistent practice, proper documentation, and staying updated with financial best practices.

---

*For additional support or custom training sessions, contact our training <NAME_EMAIL>*