# Stock Management, Inventory Tracking & Import Training Video Script
## Complete User Guide for ERP Stock Management System

### Table of Contents
1. [Introduction](#introduction)
2. [System Overview](#system-overview)
3. [Getting Started](#getting-started)
4. [Stock Lifecycle](#stock-lifecycle)
5. [Core Features](#core-features)
6. [Advanced Workflows](#advanced-workflows)
7. [Import & Export Operations](#import--export-operations)
8. [Inventory Tracking & Reports](#inventory-tracking--reports)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

---

## Introduction

Welcome to the comprehensive training guide for our ERP Stock Management, Inventory Tracking, and Import System. This guide will walk you through every aspect of stock management, from basic creation to advanced inventory operations and bulk importing.

**What You'll Learn:**
- How to create and manage stock items efficiently
- Understanding the complete stock lifecycle and activity tracking
- Advanced features like assemblies, bulk operations, and inventory reconciliation
- Stock importing procedures with file validation and error handling
- Integration with other system modules (invoices, purchases, reports)
- Best practices for inventory accuracy and compliance

---

## System Overview

### Architecture Overview

```mermaid
graph TD
    A[Stock Creation] --> B[Stock Configuration]
    B --> C[Inventory Assignment]
    C --> D[Stock Activities]
    D --> E[Inventory Tracking]
    E --> F[Reports & Analytics]
    
    B --> G[Price Management]
    B --> H[UOM Configuration]
    C --> I[Stock Movements]
    D --> J[Activity Types]
    E --> K[Quantity Monitoring]
    F --> L[Valuation Reports]
```

### Key Components

1. **Stock Model**: Core entity containing all stock/product data
2. **Stock Activities**: Individual movements (takein, takeout, sold, damaged, etc.)
3. **Inventory Management**: Warehouse and location tracking
4. **Stock Prices**: Multi-currency pricing with categories
5. **Unit of Measure (UOM)**: Flexible measurement units
6. **Stock Assembly**: Product bundling and kit management

---

## Getting Started

### Accessing the Stock Module

**Navigation Path:**
```
Dashboard → إدارة المستودعات → المنتجات/الخدمات → List Stocks
```

### Initial Setup

1. **User Permissions Required:**
   - `view_stock` - Basic viewing access
   - `add_stock` - Create new stock items
   - `change_stock` - Edit existing stock items
   - `delete_stock` - Remove stock items
   - `import_stock` - Import stock data
   - `export_stock` - Export stock data
   - `view_stock_qties` - View quantity balances
   - `view_stock_cost` - View purchase costs

2. **Default Settings:**
   - Default currency (configurable per tenant)
   - Default inventory location
   - Default accounts (income, COGS, purchase)
   - Reorder point thresholds

---

## Stock Lifecycle

### Complete Workflow

```mermaid
stateDiagram-v2
    [*] --> Created: Create Stock Item
    Created --> Configured: Set Prices & UOM
    Configured --> Active: Activate Stock
    Active --> InStock: Stock Takein
    Active --> OutStock: Stock Takeout
    InStock --> Sold: Sales Activity
    InStock --> Transferred: Transfer Activity
    InStock --> Damaged: Damage Activity
    OutStock --> Restocked: Restock Activity
    Sold --> [*]
    Active --> Inactive: Deactivate
    Inactive --> [*]
```

### Stock Activity Types

| Activity Type | Description | Impact on Inventory |
|---------------|-------------|-------------------|
| مدخل (Takein) | Stock received into inventory | Increases quantity |
| مخرج (Takeout) | Stock removed from inventory | Decreases quantity |
| مبيع (Sold) | Stock sold to customers | Decreases quantity |
| مردود صالح (Returned) | Good returns from customers | Increases quantity |
| تالف (Damaged) | Damaged/unusable stock | Decreases quantity |
| نقل (Transfer) | Transfer between inventories | Neutral (location change) |
| تجميع (Assembly) | Assembly/kit creation | Component decrease |

---

## Core Features

### 1. Creating Stock Items

#### Method 1: Standard Creation
```python
# Navigate to: إدارة المستودعات → المنتجات/الخدمات → Add Stock
# Required fields:
- Name (اسم الصنف)
- SKU (optional but recommended)
- Client Code (ترميز مخصص)
- Type: Inventory Item or Service
- Purchase Cost (كلفة الشراء)
- Supplier (المورد المفضل)
```

#### Method 2: Quick Add
```
1. Click "إضافة سريعة" button
2. Fill minimal required fields
3. System auto-generates codes
4. Save and continue editing for details
```

### 2. Stock Configuration

**Basic Information:**
- **Name**: Product/service name
- **SKU**: Stock Keeping Unit code
- **Client Code**: Custom internal code
- **Category**: Product categorization
- **Supplier**: Preferred supplier

**Financial Settings:**
- **Purchase Cost**: Base cost in default currency
- **Income Account**: Revenue account for sales
- **COGS Account**: Cost of goods sold account
- **Purchase Account**: Asset/expense account

**Inventory Settings:**
- **Is Service**: Toggle for service items
- **Reorder Point**: Minimum quantity threshold
- **Show in POS**: Display in point of sale
- **Assembly**: Enable for kit/bundle products

### 3. Stock Activities Management

#### Activity Creation Process
1. **Select Activity Type**: Choose from available activity types
2. **Enter Quantities**: Specify quantities and units
3. **Set Location**: Select inventory/warehouse
4. **Add Costs**: Enter purchase costs if applicable
5. **Commit Activity**: Finalize to update inventory

#### Activity Tracking
```
Activity History includes:
- Transaction reference
- Date and time
- User who performed action
- Quantities involved
- Cost information
- Inventory location
- Commitment status
```

### 4. Pricing and Units

**Price Management:**
- Multiple price categories (مفرد، توزيع، etc.)
- Multi-currency support
- Customer-specific pricing
- Volume-based pricing tiers

**Unit of Measure (UOM):**
```
UOM Configuration:
- Base unit (e.g., Piece)
- Conversion units (e.g., Box = 12 Pieces)
- Barcode assignment
- Category grouping
```

---

## Advanced Workflows

### 1. Stock Assembly Management

#### Creating Assemblies
```mermaid
flowchart LR
    A[Select Parent Stock] --> B[Enable Assembly]
    B --> C[Add Components]
    C --> D[Set Quantities]
    D --> E[Configure Pricing]
    E --> F[Save Assembly]
```

#### Assembly Operations
- **Component Management**: Add/remove assembly components
- **Quantity Calculation**: Auto-calculate based on components
- **Cost Calculation**: Aggregate component costs
- **Inventory Impact**: Track component consumption

### 2. Bulk Operations

#### Bulk Stock Management
```
Available Bulk Operations:
1. Bulk Delete - Remove multiple stocks
2. Bulk Category Change - Update categories
3. Bulk POS Settings - Show/hide in POS
4. Bulk Favorite Settings - Mark as favorites
5. Bulk Export - Export selected stocks
```

#### Bulk Activity Processing
```
1. Select multiple stock items
2. Choose bulk operation type
3. Configure operation parameters
4. Execute with confirmation
5. Review operation results
```

### 3. Inventory Reconciliation

#### Reconciliation Process
1. **Generate Worksheet**: Create inventory count sheet
2. **Physical Count**: Perform actual inventory count
3. **Compare Quantities**: System vs. physical counts
4. **Adjust Differences**: Create adjustment activities
5. **Finalize Reconciliation**: Commit adjustments

---

## Import & Export Operations

### 1. Stock Import Process

#### Supported Import Types
```
Import Categories:
- stock: Basic stock items
- globalstockprice: Stock pricing data
- globalunitofmeasure: UOM configurations
- activitytakein: Stock intake activities
- activitytakeout: Stock outgoing activities
- activitypurchase: Purchase activities
```

#### Import File Format
**Excel Template Structure:**
```
Row 1: Field Types (metadata)
Row 2: Field Names (headers)
Row 3+: Data rows
```

**Required Fields for Stock Import:**
- `name`: Stock item name
- `sku`: Stock keeping unit (optional)
- `client_code`: Custom code (optional)
- `supplier`: Supplier name/reference
- `purchase_cost`: Base purchase cost
- `is_service`: Boolean (true/false)

#### Import Validation Rules
```python
Validation Checks:
1. At least one identifier required (name, sku, or client_code)
2. Supplier must exist in system
3. Purchase cost must be valid decimal
4. Currency must be valid
5. Category must exist if specified
6. UOM must be valid if specified
```

### 2. Import Error Handling

#### Error Types and Solutions
```mermaid
flowchart TD
    A[Upload File] --> B{Validation Check}
    B -->|Pass| C[Import Success]
    B -->|Fail| D[Show Error Table]
    D --> E[Fix Data Issues]
    E --> F[Re-upload File]
    F --> B
```

**Common Import Errors:**
- **Missing Required Fields**: Ensure name, sku, or client_code provided
- **Invalid Supplier**: Supplier must exist in system
- **Duplicate Codes**: SKU and client_code must be unique
- **Invalid Currency**: Currency code must be valid
- **Format Errors**: Decimal fields must be properly formatted

### 3. Export Operations

#### Export Formats
- **Excel**: Full data export with formatting
- **CSV**: Comma-separated values for external systems
- **PDF**: Formatted reports for printing

#### Export Filters
```
Export Options:
- Date range filtering
- Category filtering
- Supplier filtering
- Active/inactive status
- Service vs. inventory items
```

---

## Inventory Tracking & Reports

### 1. Quantity Monitoring

#### Real-time Quantity Tracking
```mermaid
graph LR
    A[Stock Activity] --> B[Update Quantities]
    B --> C[Check Reorder Point]
    C --> D{Below Threshold?}
    D -->|Yes| E[Generate Alert]
    D -->|No| F[Continue Monitoring]
    E --> G[Notification System]
```

#### Quantity Calculation Logic
```python
# Quantity calculation formula:
qty_in = Sum(TAKEIN + RETURNED + VARIANT_IN activities)
qty_out = Sum(TAKEOUT + SOLD + DAMAGED + VARIANT_OUT activities)
current_quantity = qty_in - qty_out
```

### 2. Stock Reports

#### Available Reports
1. **Stock Valuation Report** (`قيمة المخزون حسب الصنف`)
   - Total inventory value by stock item
   - Cost basis calculations
   - Currency conversions

2. **Stock Quantities Report** (`كميات المخزون حسب الصنف`)
   - Current quantities by stock item
   - Reorder point analysis
   - Movement history

3. **Stock Worksheet** (`قائمة الجرد المخزني`)
   - Physical count worksheet
   - Variance analysis
   - Adjustment recommendations

4. **Low Stock Alerts** (`تنبيهات نفاد الكمية`)
   - Items below reorder point
   - Suggested reorder quantities
   - Purchase order generation

#### Report Filtering Options
```
Filter Criteria:
- Date ranges
- Stock categories
- Inventory locations
- Suppliers
- Activity types
- Value thresholds
```

### 3. Dashboard Analytics

#### Key Performance Indicators
- **Total Inventory Value**: Aggregate stock valuation
- **Total SKUs**: Number of active stock items
- **Low Stock Items**: Items requiring attention
- **Average Stock per SKU**: Inventory distribution metrics

#### Visual Analytics
- Stock value trends over time
- Category-wise distribution
- Movement velocity analysis
- Supplier performance metrics

---

## Integration Features

### 1. Sales Integration

**Invoice Integration:**
- Real-time stock commitment on invoice creation
- Automatic quantity updates on invoice posting
- Stock availability validation
- Assembly component tracking

### 2. Purchase Integration

**Bill Integration:**
- Automatic stock receipt on bill posting
- Cost updates from purchase prices
- Supplier performance tracking
- Purchase order fulfillment

### 3. Accounting Integration

**Automatic Journal Entries:**
```
Stock Takein:
- Debit: Inventory Asset Account
- Credit: Accounts Payable/Cash

Stock Takeout:
- Debit: Cost of Goods Sold
- Credit: Inventory Asset Account

Stock Sale:
- Debit: Accounts Receivable
- Credit: Sales Revenue
- Debit: COGS
- Credit: Inventory Asset
```

---

## Best Practices

### 1. Data Accuracy

**Validation Checklist:**
- [ ] Stock names are descriptive and consistent
- [ ] SKU codes follow company standards
- [ ] Purchase costs are current and accurate
- [ ] Reorder points are properly set
- [ ] Categories are logically organized

### 2. Workflow Efficiency

**Time-Saving Tips:**
- Use bulk operations for similar tasks
- Set up stock templates for common items
- Enable auto-generation of codes
- Use quick add for rapid entry
- Implement barcode scanning

### 3. Inventory Control

**Control Measures:**
- Regular physical counts and reconciliation
- Proper user permission management
- Activity audit trail monitoring
- Reorder point optimization
- Supplier performance evaluation

### 4. Import Best Practices

**Import Guidelines:**
- Always download and use official templates
- Validate data before importing
- Test with small batches first
- Keep backup of original data
- Review import results carefully

---

## Troubleshooting

### Common Issues and Solutions

#### Issue 1: Import File Rejected
**Symptoms:** Error message "الملف المدخل غير متطابقة مع نموذج الاستيراد"
**Solutions:**
- Download fresh template from system
- Ensure all required columns are present
- Check file format (Excel .xlsx)
- Verify data types match template

#### Issue 2: Stock Quantities Incorrect
**Symptoms:** Displayed quantities don't match physical count
**Solutions:**
- Review all stock activities for the item
- Check for uncommitted activities
- Verify activity types are correct
- Perform inventory reconciliation

#### Issue 3: Assembly Components Not Updating
**Symptoms:** Component quantities not decreasing on assembly creation
**Solutions:**
- Ensure assembly is properly configured
- Check component stock availability
- Verify assembly activity is committed
- Review assembly calculation logic

#### Issue 4: Import Validation Errors
**Symptoms:** Rows highlighted in red during import
**Solutions:**
- Check required field completion
- Verify supplier exists in system
- Ensure unique codes (SKU, client_code)
- Validate decimal number formats

### Getting Help

**Support Resources:**
- User manual (this guide)
- Video tutorials
- Help desk: <EMAIL>
- Training sessions: Monthly webinars

---

## Quick Reference

### Keyboard Shortcuts

| Action | Shortcut |
|--------|----------|
| New Stock | Ctrl+N |
| Save Stock | Ctrl+S |
| Search Stocks | Ctrl+F |
| Quick Add | Ctrl+Shift+A |
| Bulk Operations | Ctrl+B |

### Common Tasks

| Task | Navigation Path |
|------|-----------------|
| View all stocks | إدارة المستودعات → المنتجات/الخدمات |
| Create stock | المنتجات/الخدمات → Add |
| Import stocks | المنتجات/الخدمات → Import |
| Stock reports | التقارير → تقارير المخزون |
| Stock activities | إدارة المستودعات → النشاطات المخزنية |

---

## Conclusion

This comprehensive guide covers all aspects of the stock management, inventory tracking, and import system. Regular practice with these features will ensure efficient and accurate inventory management.

**Next Steps:**
1. Practice creating test stock items
2. Explore import functionality with sample data
3. Set up inventory monitoring and alerts
4. Schedule regular training updates

**Remember:** The key to mastering the stock system is consistent practice, proper data validation, and staying updated with new features and best practices.

---

*For additional support or custom training sessions, contact our training <NAME_EMAIL>*
