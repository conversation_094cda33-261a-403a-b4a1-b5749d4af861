# Step-by-Step Bootstrap to Tailwind CSS Migration Guide

## Overview

This is a detailed, actionable step-by-step guide to migrate the iCRM system from Bootstrap 5.3.0-alpha3 to Tailwind CSS. Each step includes specific commands, code examples, and verification steps based on the actual project structure.

## Current Project Analysis

**Current Setup:**
- Django 4.2.16 with Bootstrap 5.3.0-alpha3
- crispy-bootstrap5==0.7 for form rendering
- Static files served via WhiteNoise
- RTL support for Arabic language
- HTMX for dynamic interactions
- Custom CSS in `backend/theme/static/css/`

**Key Files to Migrate:**
- Base template: `backend/abstract/templates/abstract/_base.html`
- Head template: `backend/abstract/templates/abstract/includes/head.html`
- Main CSS: `backend/theme/static/css/main.css`
- Bootstrap bundles: `backend/theme/static/css/style.bundle.css` & `style.bundle.rtl.css`

## Prerequisites

- Node.js 18+ installed
- Python 3.11+ and Django 4.2.16
- Git for version control
- Code editor with Tailwind CSS IntelliSense
- pnpm package manager (as per user preference)

## Phase 1: Environment Setup and Build System

### Step 1: Initialize Node.js Project

1. **Create package.json in project root:**
```bash
cd /Users/<USER>/TwoAbstracts/iCRM
pnpm init
```

2. **Install Tailwind CSS and dependencies:**
```bash
pnpm add -D tailwindcss@latest postcss@latest autoprefixer@latest
pnpm add -D @tailwindcss/forms @tailwindcss/typography @tailwindcss/aspect-ratio
pnpm add -D webpack@latest webpack-cli@latest css-loader@latest mini-css-extract-plugin@latest postcss-loader@latest
```

3. **Verify installation:**
```bash
npx tailwindcss --help
```

### Step 2: Configure Tailwind CSS

1. **Generate Tailwind config file:**
```bash
npx tailwindcss init -p
```

2. **Update `tailwind.config.js` with project-specific paths and production optimizations:**
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './backend/**/templates/**/*.html',
    './backend/**/static/**/*.js',
    './backend/**/forms/**/*.py',
    './gini_plus/**/templates/**/*.html',
  ],
  // Safelist classes that might be dynamically generated
  safelist: [
    // Bootstrap compatibility classes
    'dropdown-menu',
    'dropdown-item',
    'dropdown-item-indicator',
    'modal-open',
    'modal-backdrop',
    'show',
    'fade',
    'active',
    'disabled',
    // Tom-Select classes
    'ts-dropdown',
    'ts-control',
    'ts-input',
    'no-results',
    'create',
    // Dynamic utility classes
    'bg-primary-50',
    'text-primary-600',
    'bg-primary-100',
    'text-primary-700',
    // Spinner classes
    'spinner-border',
    'spinner-border-sm',
    // HTMX loading states
    'htmx-request',
    'htmx-swapping',
  ],
  theme: {
    extend: {
      colors: {
        // Map existing Bootstrap colors from current CSS variables
        primary: {
          DEFAULT: '#1b84ff',
          50: '#eff8ff',
          100: '#dbeafe',
          500: '#1b84ff',
          600: '#056ee9',
          700: '#0b5394',
        },
        secondary: '#f1f1f4',
        success: '#17c653',
        info: '#7239ea',
        warning: '#f6c000',
        danger: '#f8285a',
        dark: '#1e2129',
        light: '#f9f9f9',
        gray: {
          100: '#f9f9f9',
          200: '#f1f1f4',
          300: '#dbdfe9',
          400: '#c4cada',
          500: '#99a1b7',
          600: '#78829d',
          700: '#4b5675',
          800: '#252f4a',
          900: '#071437',
        }
      },
      fontFamily: {
        sans: ['Rubik', 'sans-serif'],
        mono: ['Rubik', 'monospace'],
      },
      fontSize: {
        'xs': '.75rem',
        'sm': '.875rem',
        'base': '1rem',
        'lg': '1.125rem',
        'xl': '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        'sm': '.425rem',
        'DEFAULT': '.475rem',
        'lg': '.625rem',
        'xl': '1rem',
        '2xl': '2rem',
      },
      boxShadow: {
        'sm': '0 .1rem 1rem .25rem rgba(0, 0, 0, 0.05)',
        'DEFAULT': '0 .5rem 1.5rem .5rem rgba(0, 0, 0, 0.075)',
        'lg': '0 1rem 2rem 1rem rgba(0, 0, 0, 0.1)',
      },
      // Animation for loading states
      animation: {
        'spin-slow': 'spin 2s linear infinite',
        'pulse-fast': 'pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      // Z-index scale
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
  // Production optimizations
  corePlugins: {
    // Disable unused core plugins for smaller bundle size
    preflight: true, // Keep for CSS reset
    container: true, // Keep for layout
    accessibility: true, // Keep for a11y
    pointerEvents: true, // Keep for interactions
    visibility: true, // Keep for show/hide
    position: true, // Keep for positioning
    inset: true, // Keep for positioning
    zIndex: true, // Keep for layering
    order: true, // Keep for flex/grid
    gridColumn: true, // Keep for grid
    gridColumnStart: true, // Keep for grid
    gridColumnEnd: true, // Keep for grid
    gridRow: true, // Keep for grid
    gridRowStart: true, // Keep for grid
    gridRowEnd: true, // Keep for grid
    float: true, // Keep for compatibility
    clear: true, // Keep for compatibility
    margin: true, // Keep for spacing
    padding: true, // Keep for spacing
    space: true, // Keep for spacing
    width: true, // Keep for sizing
    minWidth: true, // Keep for sizing
    maxWidth: true, // Keep for sizing
    height: true, // Keep for sizing
    minHeight: true, // Keep for sizing
    maxHeight: true, // Keep for sizing
    fontSize: true, // Keep for typography
    fontFamily: true, // Keep for typography
    fontWeight: true, // Keep for typography
    lineHeight: true, // Keep for typography
    letterSpacing: true, // Keep for typography
    textAlign: true, // Keep for typography
    textColor: true, // Keep for colors
    textDecoration: true, // Keep for typography
    textTransform: true, // Keep for typography
    verticalAlign: true, // Keep for alignment
    whitespace: true, // Keep for text
    wordBreak: true, // Keep for text
    backgroundColor: true, // Keep for colors
    backgroundImage: true, // Keep for backgrounds
    backgroundPosition: true, // Keep for backgrounds
    backgroundRepeat: true, // Keep for backgrounds
    backgroundSize: true, // Keep for backgrounds
    borderColor: true, // Keep for borders
    borderRadius: true, // Keep for borders
    borderStyle: true, // Keep for borders
    borderWidth: true, // Keep for borders
    boxShadow: true, // Keep for effects
    opacity: true, // Keep for effects
    transform: true, // Keep for animations
    transformOrigin: true, // Keep for animations
    scale: true, // Keep for animations
    rotate: true, // Keep for animations
    translate: true, // Keep for animations
    skew: true, // Keep for animations
    transitionProperty: true, // Keep for animations
    transitionDuration: true, // Keep for animations
    transitionTimingFunction: true, // Keep for animations
    transitionDelay: true, // Keep for animations
    animation: true, // Keep for animations
  }
}
```

3. **Create PostCSS config (`postcss.config.js`):**
```javascript
module.exports = {
  plugins: {
    'postcss-import': {},
    'tailwindcss/nesting': 'postcss-nested',
    tailwindcss: {},
    autoprefixer: {},
    ...(process.env.NODE_ENV === 'production' ? {
      cssnano: {
        preset: 'default',
      },
    } : {}),
  },
}
```

### Step 3: Setup Build System

1. **Create `webpack.config.js` in project root:**
```javascript
const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = {
  entry: {
    main: './frontend/src/main.css',
    'main-rtl': './frontend/src/main-rtl.css',
  },
  output: {
    path: path.resolve(__dirname, 'backend/theme/static/css/dist'),
    filename: '[name].js',
    clean: true,
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader',
        ],
      },
    ],
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: '[name].css',
    }),
  ],
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  devtool: process.env.NODE_ENV === 'production' ? false : 'source-map',
};
```

2. **Create source directory structure:**
```bash
mkdir -p frontend/src
mkdir -p backend/theme/static/css/dist
```

3. **Create main Tailwind CSS file (`frontend/src/main.css`):**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import existing custom CSS variables for compatibility */
@import '../../backend/theme/static/css/main.css';

/* Custom component classes to match Bootstrap patterns */
@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 ease-in-out;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-600 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-success {
    @apply bg-success text-white hover:bg-green-600 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-danger {
    @apply bg-danger text-white hover:bg-red-600 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-warning {
    @apply bg-warning text-white hover:bg-yellow-600 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-info {
    @apply bg-info text-white hover:bg-purple-600 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-outline-primary {
    @apply border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary-500;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  .card-title {
    @apply text-lg font-semibold text-gray-900 mb-2;
  }

  .card-text {
    @apply text-gray-600;
  }

  /* Table Components */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table thead th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200;
  }

  .table tbody td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-100;
  }

  .table-striped tbody tr:nth-child(even) {
    @apply bg-gray-50;
  }

  .table-hover tbody tr:hover {
    @apply bg-gray-50;
  }

  /* Form Components */
  .form-control {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white transition-colors duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-check-input {
    @apply h-4 w-4 text-primary focus:ring-primary-500 border-gray-300 rounded;
  }

  .form-check-label {
    @apply text-sm text-gray-700 ml-2;
  }

  /* Alert Components */
  .alert {
    @apply p-4 rounded-md border mb-4;
  }

  .alert-success {
    @apply bg-green-50 border-green-200 text-green-800;
  }

  .alert-danger {
    @apply bg-red-50 border-red-200 text-red-800;
  }

  .alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
  }

  .alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
  }

  /* Modal Components */
  .modal-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  }

  .modal {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }

  .modal-dialog {
    @apply flex min-h-full items-center justify-center p-4;
  }

  .modal-content {
    @apply bg-white rounded-lg shadow-xl max-w-lg w-full;
  }

  .modal-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .modal-body {
    @apply px-6 py-4;
  }

  .modal-footer {
    @apply px-6 py-4 border-t border-gray-200 flex justify-end space-x-2;
  }
}

/* Custom utilities */
@layer utilities {
  .text-truncate {
    @apply truncate;
  }

  .visually-hidden {
    @apply sr-only;
  }

  /* Spinner utilities for loading states */
  .spinner-border {
    @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
  }

  .spinner-border-sm {
    @apply w-3 h-3;
  }

  /* Float utilities for compatibility */
  .float-end {
    @apply float-right;
  }

  .float-start {
    @apply float-left;
  }
}
```

4. **Create RTL-specific Tailwind CSS file (`frontend/src/main-rtl.css`):**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import existing RTL custom CSS variables for compatibility */
@import '../../backend/theme/static/css/style.bundle.rtl.css';

/* RTL-specific overrides */
@layer utilities {
  /* Float utilities for RTL */
  .float-end {
    @apply float-left;
  }

  .float-start {
    @apply float-right;
  }

  /* Text alignment for RTL */
  .text-start {
    @apply text-right;
  }

  .text-end {
    @apply text-left;
  }

  /* Margin and padding RTL adjustments */
  .ml-auto {
    @apply mr-auto ml-0;
  }

  .mr-auto {
    @apply ml-auto mr-0;
  }

  /* Border radius RTL adjustments */
  .rounded-l-md {
    @apply rounded-r-md rounded-l-none;
  }

  .rounded-r-md {
    @apply rounded-l-md rounded-r-none;
  }

  /* Transform RTL adjustments */
  .translate-x-full {
    @apply -translate-x-full;
  }

  .-translate-x-full {
    @apply translate-x-full;
  }

  /* Positioning RTL adjustments */
  .left-0 {
    @apply right-0 left-auto;
  }

  .right-0 {
    @apply left-0 right-auto;
  }

  /* Flexbox RTL adjustments */
  .justify-start {
    @apply justify-end;
  }

  .justify-end {
    @apply justify-start;
  }

  /* Space adjustments for RTL */
  .space-x-2 > :not([hidden]) ~ :not([hidden]) {
    @apply ml-2 mr-0;
  }

  .space-x-3 > :not([hidden]) ~ :not([hidden]) {
    @apply ml-3 mr-0;
  }

  .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    @apply ml-4 mr-0;
  }
}

/* RTL-specific component overrides */
@layer components {
  /* Modal positioning for RTL */
  .modal-dialog {
    @apply text-right;
  }

  /* Form controls RTL */
  .form-control {
    @apply text-right;
  }

  /* Button groups RTL */
  .btn-group .btn:first-child {
    @apply rounded-r-md rounded-l-none;
  }

  .btn-group .btn:last-child {
    @apply rounded-l-md rounded-r-none;
  }

  /* Dropdown RTL */
  .dropdown-menu {
    @apply right-0 left-auto;
  }

  /* Sidebar RTL adjustments */
  #kt_app_sidebar {
    @apply right-0 left-auto;
  }

  #kt_app_sidebar.lg\:translate-x-0 {
    @apply lg:translate-x-0;
  }

  /* Navigation RTL */
  .breadcrumb {
    @apply flex-row-reverse;
  }
}
```

5. **Add build scripts to `package.json`:**
```json
{
  "name": "icrm-frontend",
  "version": "1.0.0",
  "scripts": {
    "build": "NODE_ENV=production webpack --mode=production",
    "dev": "webpack --mode=development --watch",
    "build-css": "tailwindcss -i ./frontend/src/main.css -o ./backend/theme/static/css/dist/main.css --watch",
    "build-css-rtl": "tailwindcss -i ./frontend/src/main-rtl.css -o ./backend/theme/static/css/dist/main-rtl.css --watch"
  },
  "devDependencies": {
    "tailwindcss": "^3.4.0",
    "postcss": "^8.4.32",
    "autoprefixer": "^10.4.16",
    "webpack": "^5.89.0",
    "webpack-cli": "^5.1.4",
    "css-loader": "^6.8.1",
    "mini-css-extract-plugin": "^2.7.6",
    "postcss-loader": "^7.3.3",
    "@tailwindcss/forms": "^0.5.7",
    "@tailwindcss/typography": "^0.5.10",
    "@tailwindcss/aspect-ratio": "^0.4.2",
    "cssnano": "^6.0.0",
    "postcss-import": "^15.1.0",
    "postcss-nested": "^6.0.0",
    "purgecss": "^5.0.0"
  }
}
```

### Step 4: Update Django Settings

1. **Install django-compressor (if not already installed):**
```bash
pip install django-compressor
```

2. **Update `backend/settings.py` to add compressor support:**
```python
# Add to INSTALLED_APPS (after existing apps)
INSTALLED_APPS = [
    "django_multitenant",
    # ... existing contrib apps ...
    "crispy_forms",
    "crispy_bootstrap5",  # Keep for now during transition
    # ... other existing apps ...
    "compressor",  # Add this
    # ... rest of existing apps ...
]

# Add to STATICFILES_FINDERS (if not already present)
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'compressor.finders.CompressorFinder',
]

# Compressor settings
COMPRESS_ENABLED = not DEBUG
COMPRESS_CSS_FILTERS = [
    'compressor.filters.css_default.CssAbsoluteFilter',
    'compressor.filters.cssmin.rCSSMinFilter',
]
COMPRESS_JS_FILTERS = [
    'compressor.filters.jsmin.JSMinFilter',
]

# Add feature flag for gradual migration
# Note: get_setting function is already imported in settings.py
USE_TAILWIND = get_setting("USE_TAILWIND", False, cast=bool)
```

3. **Update TEMPLATES configuration to match existing structure:**
```python
# In backend/settings.py, update TEMPLATES configuration
TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "django.template.context_processors.i18n",  # Add this if not present
                "backend.erp.context_processors.prefs",
                "backend.erp.context_processors.subscription_context",
                "backend.abstract.context_processors.ui_framework",  # Add this line
            ],
        },
    },
]
```

### Step 5: Test Build System

1. **Build Tailwind CSS:**
```bash
pnpm run build
```

2. **Verify output files exist:**
```bash
ls -la backend/theme/static/css/dist/
# Should show: main.css, main-rtl.css, main.js, main-rtl.js
```

3. **Start development watch mode:**
```bash
pnpm run dev
```

4. **Test CSS generation:**
```bash
# In another terminal, test individual builds
pnpm run build-css
pnpm run build-css-rtl
```

## Phase 2: Base Template Migration

### Step 6: Create Backup and Feature Flag System

1. **Create backup of current templates:**
```bash
# Backup base template
cp backend/abstract/templates/abstract/_base.html backend/abstract/templates/abstract/_base_bootstrap.html

# Backup head template
cp backend/abstract/templates/abstract/includes/head.html backend/abstract/templates/abstract/includes/head_bootstrap.html
```

2. **Create context processor for feature flag:**
```bash
# Create the context processor file
touch backend/abstract/context_processors.py
```

```python
# Add to backend/abstract/context_processors.py
from django.conf import settings

def ui_framework(request):
    """
    Context processor to provide UI framework information to templates.
    """
    return {
        'USE_TAILWIND': getattr(settings, 'USE_TAILWIND', False),
        'CURRENT_LANGUAGE': request.LANGUAGE_CODE,
    }
```

3. **Add context processor to settings:**
```python
# In backend/settings.py, update TEMPLATES configuration
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
                'backend.abstract.context_processors.ui_framework',  # Add this line
            ],
        },
    },
]
```

### Step 7: Update Head Section

1. **Create new Tailwind head include (`backend/abstract/templates/abstract/includes/head_tailwind.html`):**
```html
{% load static %}
{% load compress %}
{% load i18n %}

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    {% include 'abstract/includes/meta.html' %}

    <!-- Tailwind CSS -->
    {% get_current_language as CURRENT_LANGUAGE %}
    {% if CURRENT_LANGUAGE == 'en' %}
        {% compress css %}
        <link href="{% static 'css/dist/main.css' %}" rel="stylesheet" type="text/css"/>
        {% endcompress %}
    {% else %}
        {% compress css %}
        <link href="{% static 'css/dist/main-rtl.css' %}" rel="stylesheet" type="text/css"/>
        {% endcompress %}
    {% endif %}

    <!-- Keep essential external CSS -->
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css' rel='stylesheet' />

    <!-- Custom CSS (preserve existing customizations) -->
    <link href="{% static 'css/main.css' %}" rel="stylesheet"/>
    <link rel="stylesheet" href="{% static 'css/nprogress.css' %}" type="text/css">

    <!-- Essential JavaScript -->
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js'></script>
    <script type="text/javascript" src="{% url 'javascript-catalog' %}"></script>
</head>
```

2. **Update main head include to conditionally load frameworks:**
```html
<!-- Update backend/abstract/templates/abstract/includes/head.html -->
{% load static %}
{% load i18n %}

{% if USE_TAILWIND %}
    {% include 'abstract/includes/head_tailwind.html' %}
{% else %}
    <!-- Keep existing Bootstrap head content -->
    <head>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
        <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
        {% include 'abstract/includes/meta.html' %}
        <!-- CSS files -->
        {% get_current_language as CURRENT_LANGUAGE %}
        {% if CURRENT_LANGUAGE == 'en' %}
            <link href="{% static "css/style.bundle.css" %}" rel="stylesheet" type="text/css"/>
            <link href="{% static "plugins/global/plugins.bundle.css" %}" rel="stylesheet" type="text/css"/>
        {% else %}
            <link href="{% static "css/style.bundle.rtl.css" %}" rel="stylesheet" type="text/css"/>
            <link href="{% static "plugins/global/plugins.bundle.rtl.css" %}" rel="stylesheet" type="text/css"/>
        {% endif %}
        <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css' rel='stylesheet' />
        <link href="{% static 'css/main.css' %}" rel="stylesheet"/>
        <link rel="stylesheet" href="{% static 'css/nprogress.css' %}" type="text/css">
        <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js'></script>
        <script type="text/javascript" src="{% url 'javascript-catalog' %}"></script>
        <script></script>
    </head>
{% endif %}
```

### Step 8: Create Tailwind Base Template

1. **Create Tailwind version of base template (`backend/abstract/templates/abstract/_base_tailwind.html`):**
```html
{% load static %}
{% load i18n %}
{% load global %}
{% load sidebar_menu %}

<!doctype html>
{% get_current_language as CURRENT_LANGUAGE %}
{% if CURRENT_LANGUAGE == 'en' %}
    <html lang="en" dir="ltr" class="h-full bg-gray-100">
{% else %}
    <html lang="ar" dir="rtl" class="h-full bg-gray-100">
{% endif %}

{% include 'abstract/includes/head.html' %}

<!-- Additional Tailwind-specific CSS -->
<link rel="stylesheet" type="text/css" href="{% static 'css/command-dialog.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'css/app-switcher.css' %}">
{% block extra_css %}{% endblock %}

<body hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
      class="h-full font-sans antialiased bg-gray-100"
      id="kt_app_body">

<!-- Sidebar overlay for mobile -->
<div class="fixed inset-0 z-40 lg:hidden hidden" id="sidebar-overlay" onclick="closeSidebar()">
    <div class="fixed inset-0 bg-black bg-opacity-50"></div>
</div>

<!-- Pre-rendered icon templates for JS compatibility -->
<div id="menu-icon-template" class="hidden">{% get_icon 'menu-collapse' 32 %}</div>
<div id="arrow-icon-template" class="hidden">{% get_icon 'arrow-collapse' 32 %}</div>

<script src="{% static 'js/demo-theme.js' %}"></script>

<div class="flex h-screen bg-gray-100" id="kt_app_root">
    <!-- Sidebar -->
    {% block sidebar %}
    {% generate_sidebar_menu %}
    {% if show_app_sidebar %}
        {% include 'abstract/includes/app_sidebar_tailwind.html' %}
    {% else %}
        {% include 'abstract/includes/sidebar_tailwind.html' %}
    {% endif %}
    {% endblock %}

    <!-- Main content wrapper -->
    <div class="flex-1 flex flex-col min-w-0 overflow-hidden">
        <!-- Navigation -->
        {% block nav %}
            {% include 'abstract/includes/navbar_tailwind.html' %}
        {% endblock %}

        <!-- Main content -->
        <main class="flex-1 overflow-y-auto bg-gray-50">
            <!-- Toolbar/Header -->
            {% if title or breadcrumbs or actions %}
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 py-4 sm:px-6 lg:px-8">
                    <div class="flex items-center justify-between">
                        <!-- Page title and breadcrumbs -->
                        <div class="flex-1 min-w-0">
                            {% if breadcrumbs %}
                                <nav class="flex mb-2" aria-label="Breadcrumb">
                                    <ol class="flex items-center space-x-2 text-sm text-gray-500">
                                        {% for breadcrumb in breadcrumbs %}
                                            <li class="flex items-center">
                                                {% if not forloop.first %}
                                                    <svg class="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                                    </svg>
                                                {% endif %}
                                                {% if breadcrumb.url %}
                                                    <a href="{{ breadcrumb.url }}" class="hover:text-gray-700 transition-colors">{{ breadcrumb.title }}</a>
                                                {% else %}
                                                    <span class="text-gray-900 font-medium">{{ breadcrumb.title }}</span>
                                                {% endif %}
                                            </li>
                                        {% endfor %}
                                    </ol>
                                </nav>
                            {% endif %}

                            {% if title %}
                                <h1 class="text-2xl font-bold text-gray-900 leading-tight">{{ title|safe }}</h1>
                            {% endif %}
                        </div>

                        <!-- Page actions -->
                        <div class="flex items-center space-x-3">
                            <div id="page_actions" class="flex items-center space-x-2">
                                {% block actions %}{% endblock %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Page content -->
            <div class="px-4 py-6 sm:px-6 lg:px-8">
                <div class="max-w-none">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Include existing scripts -->
{% include 'abstract/includes/scripts.html' %}

<!-- Additional Tailwind-compatible scripts -->
<script src="{% static 'js/command-dialog.js' %}" defer></script>
<script src="{% static 'js/app-switcher.js' %}" defer></script>

{% block selectable %}
    {% if selectable %}
        {% include 'abstract/includes/selectable_rows.html' %}
    {% endif %}
{% endblock selectable %}

{% block extra_js %}{% endblock extra_js %}

<script>
// Enhanced mobile sidebar functionality for Tailwind
function isMobile() {
    return window.innerWidth < 1024; // lg breakpoint in Tailwind
}

function isRTL() {
    return document.documentElement.dir === 'rtl' || document.body.dir === 'rtl';
}

function showOverlay() {
    if (isMobile()) {
        const overlay = document.getElementById('sidebar-overlay');
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    }
}

function hideOverlay() {
    const overlay = document.getElementById('sidebar-overlay');
    if (overlay) {
        overlay.classList.add('hidden');
    }
}

function setSidebarState(visible) {
    const sidebar = document.getElementById('kt_app_sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (!sidebar) return;

    if (isMobile()) {
        if (visible) {
            sidebar.classList.remove('-translate-x-full');
            if (overlay) overlay.classList.remove('hidden');
        } else {
            sidebar.classList.add('-translate-x-full');
            if (overlay) overlay.classList.add('hidden');
        }
    }
}

function toggleSidebar() {
    const sidebar = document.getElementById('kt_app_sidebar');
    if (!sidebar) return;

    const isVisible = !sidebar.classList.contains('-translate-x-full');
    setSidebarState(!isVisible);
}

function closeSidebar() {
    setSidebarState(false);
}

// Initialize sidebar state on page load
document.addEventListener('DOMContentLoaded', function() {
    if (isMobile()) {
        setSidebarState(false);
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    if (!isMobile()) {
        hideOverlay();
        const sidebar = document.getElementById('kt_app_sidebar');
        if (sidebar) {
            sidebar.classList.remove('-translate-x-full');
        }
    }
});
</script>

</body>
</html>
```

2. **Update main base template to conditionally use Tailwind:**
```html
<!-- Update backend/abstract/templates/abstract/_base.html -->
{% load static %}
{% load i18n %}
{% load global %}
{% load sidebar_menu %}

{% if USE_TAILWIND %}
    {% include 'abstract/_base_tailwind.html' %}
{% else %}
    <!-- Keep existing Bootstrap base template content -->
    <!doctype html>
    {% get_current_language as CURRENT_LANGUAGE %}
    {% if CURRENT_LANGUAGE == 'en' %}
        <html lang="en" dir="ltr" style="direction:ltr;">
    {% else %}
        <html lang="ar" dir="rtl" style="direction:rtl;">
    {% endif %}
    {% include 'abstract/includes/head.html' %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/command-dialog.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/app-switcher.css' %}">
    {% block extra_css %}{% endblock %}

    <!-- Rest of existing Bootstrap template content... -->
    <!-- (Keep all existing Bootstrap template code here) -->
{% endif %}
```

### Step 9: Create Tailwind Sidebar Components

1. **Create Tailwind sidebar template (`backend/abstract/templates/abstract/includes/sidebar_tailwind.html`):**
```html
{% load static %}
{% load i18n %}
{% load global %}
{% load sidebar_menu %}

<!-- Sidebar -->
<div id="kt_app_sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
    <!-- Sidebar header -->
    <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        <div class="flex items-center">
            <img class="h-8 w-auto" src="{% static 'images/logo.svg' %}" alt="iCRM">
            <span class="ml-2 text-xl font-semibold text-gray-900">iCRM</span>
        </div>
        <button type="button" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100" onclick="closeSidebar()">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <!-- Sidebar navigation -->
    <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        {% for menu_item in sidebar_menu %}
            {% if menu_item.children %}
                <!-- Menu group with children -->
                <div class="space-y-1">
                    <button type="button" class="w-full flex items-center justify-between px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 group"
                            onclick="toggleSubmenu('submenu-{{ forloop.counter }}')">
                        <div class="flex items-center">
                            {% if menu_item.icon %}
                                {% get_icon menu_item.icon 20 %}
                            {% endif %}
                            <span class="ml-3">{{ menu_item.title }}</span>
                        </div>
                        <svg class="ml-3 h-5 w-5 transform transition-transform duration-200" id="submenu-{{ forloop.counter }}-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="hidden pl-6 space-y-1" id="submenu-{{ forloop.counter }}">
                        {% for child in menu_item.children %}
                            <a href="{{ child.url }}" class="flex items-center px-2 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 {% if child.active %}bg-primary-50 text-primary-600{% endif %}">
                                {% if child.icon %}
                                    {% get_icon child.icon 16 %}
                                {% endif %}
                                <span class="ml-3">{{ child.title }}</span>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            {% else %}
                <!-- Single menu item -->
                <a href="{{ menu_item.url }}" class="flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 group {% if menu_item.active %}bg-primary-50 text-primary-600{% endif %}">
                    {% if menu_item.icon %}
                        {% get_icon menu_item.icon 20 %}
                    {% endif %}
                    <span class="ml-3">{{ menu_item.title }}</span>
                </a>
            {% endif %}
        {% endfor %}
    </nav>
</div>

<script>
function toggleSubmenu(submenuId) {
    const submenu = document.getElementById(submenuId);
    const icon = document.getElementById(submenuId + '-icon');

    if (submenu.classList.contains('hidden')) {
        submenu.classList.remove('hidden');
        icon.classList.add('rotate-90');
    } else {
        submenu.classList.add('hidden');
        icon.classList.remove('rotate-90');
    }
}
</script>
```

2. **Create Tailwind navbar template (`backend/abstract/templates/abstract/includes/navbar_tailwind.html`):**
```html
{% load static %}
{% load i18n %}
{% load global %}

<!-- Navigation -->
<header class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Mobile menu button -->
            <div class="flex items-center lg:hidden">
                <button type="button" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100" onclick="toggleSidebar()">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>

            <!-- Search -->
            <div class="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-start">
                <div class="max-w-lg w-full lg:max-w-xs">
                    <label for="search" class="sr-only">{% trans 'البحث' %}</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <input id="search" name="search" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm" placeholder="{% trans 'البحث...' %}" type="search">
                    </div>
                </div>
            </div>

            <!-- Right side navigation -->
            <div class="flex items-center space-x-4">
                <!-- Notifications -->
                <button type="button" class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z" />
                    </svg>
                </button>

                <!-- Profile dropdown -->
                <div class="relative">
                    <button type="button" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" onclick="toggleProfileMenu()">
                        <img class="h-8 w-8 rounded-full" src="{% static 'images/default-avatar.png' %}" alt="Profile">
                    </button>

                    <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50" id="profile-menu">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{% trans 'الملف الشخصي' %}</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{% trans 'الإعدادات' %}</a>
                        <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{% trans 'تسجيل الخروج' %}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
function toggleProfileMenu() {
    const menu = document.getElementById('profile-menu');
    menu.classList.toggle('hidden');
}

// Close profile menu when clicking outside
document.addEventListener('click', function(event) {
    const menu = document.getElementById('profile-menu');
    const button = event.target.closest('button');

    if (!button || !button.onclick || button.onclick.toString().indexOf('toggleProfileMenu') === -1) {
        menu.classList.add('hidden');
    }
});
</script>
```

3. **Create Tailwind app sidebar template (`backend/abstract/templates/abstract/includes/app_sidebar_tailwind.html`):**
```html
{% load static %}
{% load i18n %}
{% load global %}
{% load sidebar_menu %}

<!-- App-specific Sidebar -->
<div id="kt_app_sidebar" class="fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
    <!-- Sidebar header -->
    <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        <div class="flex items-center">
            <img class="h-8 w-auto" src="{% static 'images/logo.svg' %}" alt="iCRM">
            <span class="ml-2 text-xl font-semibold text-gray-900">iCRM</span>
            <span class="ml-2 text-sm text-gray-500">{{ app_name|default:"App" }}</span>
        </div>
        <button type="button" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100" onclick="closeSidebar()">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <!-- App-specific navigation -->
    <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        {% for menu_item in app_sidebar_menu %}
            {% if menu_item.children %}
                <!-- Menu group with children -->
                <div class="space-y-1">
                    <button type="button" class="w-full flex items-center justify-between px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 group"
                            onclick="toggleSubmenu('app-submenu-{{ forloop.counter }}')">
                        <div class="flex items-center">
                            {% if menu_item.icon %}
                                {% get_icon menu_item.icon 20 %}
                            {% endif %}
                            <span class="ml-3">{{ menu_item.title }}</span>
                        </div>
                        <svg class="ml-3 h-5 w-5 transform transition-transform duration-200" id="app-submenu-{{ forloop.counter }}-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="hidden pl-6 space-y-1" id="app-submenu-{{ forloop.counter }}">
                        {% for child in menu_item.children %}
                            <a href="{{ child.url }}" class="flex items-center px-2 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 {% if child.active %}bg-primary-50 text-primary-600{% endif %}">
                                {% if child.icon %}
                                    {% get_icon child.icon 16 %}
                                {% endif %}
                                <span class="ml-3">{{ child.title }}</span>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            {% else %}
                <!-- Single menu item -->
                <a href="{{ menu_item.url }}" class="flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 group {% if menu_item.active %}bg-primary-50 text-primary-600{% endif %}">
                    {% if menu_item.icon %}
                        {% get_icon menu_item.icon 20 %}
                    {% endif %}
                    <span class="ml-3">{{ menu_item.title }}</span>
                </a>
            {% endif %}
        {% endfor %}
    </nav>

    <!-- App switcher at bottom -->
    <div class="border-t border-gray-200 p-4">
        <button type="button" class="w-full flex items-center px-2 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900" onclick="toggleAppSwitcher()">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
            <span class="ml-3">{% trans 'تبديل التطبيق' %}</span>
        </button>
    </div>
</div>
```

## Phase 2: Crispy Forms Migration

### Step 10: Create Custom Crispy Forms Templates for Tailwind

1. **Create crispy forms template directory:**
```bash
mkdir -p backend/abstract/templates/tailwind
mkdir -p backend/abstract/templates/tailwind/bootstrap5
mkdir -p backend/abstract/templates/tailwind/bootstrap5/layout
```

2. **Create base field template (`backend/abstract/templates/tailwind/bootstrap5/field.html`):**

```html
{% load crispy_forms_field %}

<div class="mb-4">
    {% if field.label %}
        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
            {{ field.label }}
            {% if field.field.required %}
                <span class="text-red-500 ml-1">*</span>
            {% endif %}
        </label>
    {% endif %}

    {% if field.help_text %}
        <p class="text-sm text-gray-500 mb-1">{{ field.help_text }}</p>
    {% endif %}

    <div class="relative">
        {{ field|add_class:"form-control" }}
        {% if field.errors %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
        {% endif %}
    </div>

    {% if field.errors %}
        <div class="mt-1">
            {% for error in field.errors %}
                <p class="text-sm text-red-600">{{ error }}</p>
            {% endfor %}
        </div>
    {% endif %}
</div>
```

3. **Create additional crispy form templates:**

**Create form layout template (`backend/abstract/templates/tailwind/bootstrap5/layout/form.html`):**
```html
{% load crispy_forms_field %}

<form {{ form_attrs|safe }}>
    {% if form_tag %}
        {% csrf_token %}
    {% endif %}

    {% for field in form %}
        {% include 'tailwind/bootstrap5/field.html' %}
    {% endfor %}

    {% if form_show_errors and form.non_field_errors %}
        <div class="mb-4">
            {% for error in form.non_field_errors %}
                <div class="text-sm text-red-600">{{ error }}</div>
            {% endfor %}
        </div>
    {% endif %}
</form>
```

**Create checkbox field template (`backend/abstract/templates/tailwind/bootstrap5/checkbox.html`):**
```html
{% load crispy_forms_field %}

<div class="mb-4">
    <div class="flex items-center">
        {{ field|add_class:"form-check-input" }}
        {% if field.label %}
            <label for="{{ field.id_for_label }}" class="form-check-label">
                {{ field.label }}
                {% if field.field.required %}
                    <span class="text-red-500 ml-1">*</span>
                {% endif %}
            </label>
        {% endif %}
    </div>

    {% if field.help_text %}
        <p class="text-sm text-gray-500 mt-1">{{ field.help_text }}</p>
    {% endif %}

    {% if field.errors %}
        <div class="mt-1">
            {% for error in field.errors %}
                <p class="text-sm text-red-600">{{ error }}</p>
            {% endfor %}
        </div>
    {% endif %}
</div>
```

**Create select field template (`backend/abstract/templates/tailwind/bootstrap5/select.html`):**
```html
{% load crispy_forms_field %}

<div class="mb-4">
    {% if field.label %}
        <label for="{{ field.id_for_label }}" class="form-label">
            {{ field.label }}
            {% if field.field.required %}
                <span class="text-red-500 ml-1">*</span>
            {% endif %}
        </label>
    {% endif %}

    {% if field.help_text %}
        <p class="text-sm text-gray-500 mb-1">{{ field.help_text }}</p>
    {% endif %}

    <div class="relative">
        {{ field|add_class:"form-select" }}
        {% if field.errors %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
        {% endif %}
    </div>

    {% if field.errors %}
        <div class="mt-1">
            {% for error in field.errors %}
                <p class="text-sm text-red-600">{{ error }}</p>
            {% endfor %}
        </div>
    {% endif %}
</div>
```

4. **Create Tailwind modal templates (`backend/abstract/templates/abstract/includes/modals_tailwind.html`):**
```html
{% load i18n %}

{# Form Modal - Tailwind Version #}
<div class="modal hidden fixed inset-0 z-50 overflow-y-auto" id="form-modal">
    <div class="modal-backdrop fixed inset-0 bg-black bg-opacity-50" onclick="closeModal('#form-modal')"></div>
    <div class="modal-dialog flex min-h-full items-center justify-center p-4">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="modal-header px-6 py-4 border-b border-gray-200">
                <h5 class="modal-title text-lg font-semibold text-gray-900" id="form-modal-title"></h5>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('#form-modal')">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="modal-body px-6 py-4">
                <div id="form-modal-content"></div>
            </div>
        </div>
    </div>
</div>

{# HTMX Form Modal - Tailwind Version #}
<div class="modal hidden fixed inset-0 z-50 overflow-y-auto" id="form-htmx-modal">
    <div class="modal-backdrop fixed inset-0 bg-black bg-opacity-50" onclick="closeModal('#form-htmx-modal')"></div>
    <div class="modal-dialog flex min-h-full items-center justify-center p-4">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="modal-header px-6 py-4 border-b border-gray-200">
                <h5 class="modal-title text-lg font-semibold text-gray-900" id="form-htmx-modal-title"></h5>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('#form-htmx-modal')">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="modal-body px-6 py-4" id="form-htmx-modal-content">
                <!-- HTMX content will be loaded here -->
            </div>
        </div>
    </div>
</div>

{# Confirmation Modal - Tailwind Version #}
<div class="modal hidden fixed inset-0 z-50 overflow-y-auto" id="modal-confirm">
    <div class="modal-backdrop fixed inset-0 bg-black bg-opacity-50" onclick="closeModal('#modal-confirm')"></div>
    <div class="modal-dialog flex min-h-full items-center justify-center p-4">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="modal-header px-6 py-4 border-b border-gray-200">
                <h5 class="modal-title text-lg font-semibold text-gray-900" id="confirm-modal-title">{% trans 'تأكيد' %}</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('#modal-confirm')">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="modal-body px-6 py-4" id="confirm-modal-content">
                <!-- Confirmation content -->
            </div>
            <div class="modal-footer px-6 py-4 border-t border-gray-200 flex justify-end space-x-2">
                <button type="button" class="btn btn-secondary" onclick="closeModal('#modal-confirm')">{% trans 'إلغاء' %}</button>
                <button type="button" class="btn btn-danger" id="danger-confirm-modal-btn">{% trans 'تأكيد' %}</button>
            </div>
        </div>
    </div>
</div>

{# Image Modal - Tailwind Version #}
<div class="modal hidden fixed inset-0 z-50 overflow-y-auto" id="modal-image">
    <div class="modal-backdrop fixed inset-0 bg-black bg-opacity-50" onclick="closeModal('#modal-image')"></div>
    <div class="modal-dialog flex min-h-full items-center justify-center p-4">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-4xl w-full">
            <div class="modal-header px-6 py-4 border-b border-gray-200">
                <h5 class="modal-title text-lg font-semibold text-gray-900">{% trans 'معاينة الصورة' %}</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('#modal-image')">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="modal-body px-6 py-4" id="image-modal-content">
                <!-- Image content will be loaded here -->
            </div>
        </div>
    </div>
</div>
```

5. **Update Django settings for custom crispy templates:**
```python
# In backend/settings.py, add after existing crispy settings
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Add custom template directory for Tailwind crispy forms
if USE_TAILWIND:
    CRISPY_TEMPLATE_PACK = "tailwind"
    CRISPY_ALLOWED_TEMPLATE_PACKS = "tailwind"

    # Add template directory for custom crispy templates
    TEMPLATES[0]['DIRS'].append(BASE_DIR / 'backend' / 'abstract' / 'templates')
```

## Phase 3: Step-by-Step Template Migration

### Step 11: Enable Tailwind Mode

1. **Set environment variable to enable Tailwind:**
```bash
# Add to your .env file or export in terminal
export USE_TAILWIND=True
```

2. **Test the feature flag:**
```bash
# Start Django development server
python manage.py runserver

# In another terminal, start Tailwind build process
pnpm run dev
```

3. **Verify Tailwind is loading:**
- Open browser developer tools
- Check that `main.css` or `main-rtl.css` is loading from `/static/css/dist/`
- Verify Tailwind classes are being applied

### Step 12: Migrate Core Components Systematically

1. **Create component migration checklist:**
```bash
# Create a tracking file
touch migration-progress.md
```

2. **Migration priority order:**
```markdown
## Component Migration Priority

### Phase 1: Critical Infrastructure (Week 1-2)
- [x] Build system setup
- [x] Base template structure
- [x] Head template with conditional loading
- [ ] Sidebar navigation
- [ ] Top navigation bar
- [ ] Modal system
- [ ] Form field templates

### Phase 2: Core UI Components (Week 3-4)
- [ ] Button components
- [ ] Card components
- [ ] Table components
- [ ] Alert/notification components
- [ ] Form components (inputs, selects, checkboxes)
- [ ] Pagination components

### Phase 3: Application Templates (Week 5-8)
- [ ] Home dashboard
- [ ] ERP module templates
- [ ] CRM module templates
- [ ] HR module templates
- [ ] Delivery module templates
- [ ] Reporting module templates

### Phase 4: JavaScript Integration (Week 9-10)
- [ ] HTMX integration updates
- [ ] Bootstrap JS replacement
- [ ] Tom-Select styling
- [ ] CKEditor integration
- [ ] Leaflet map styling

### Phase 5: Testing & Optimization (Week 11-12)
- [ ] Cross-browser testing
- [ ] Mobile responsiveness
- [ ] Performance optimization
- [ ] Accessibility compliance
```

### Step 13: Migrate Specific Templates

1. **Start with the home dashboard:**
```bash
# Create backup
cp backend/home/<USER>/home/<USER>/home/<USER>/home/<USER>
```

2. **Update home template to use Tailwind classes:**
```html
<!-- Example migration for home dashboard -->
<!-- Before (Bootstrap): -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Dashboard</h5>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- After (Tailwind): -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Dashboard</h5>
            </div>
        </div>
    </div>
</div>
```

### Step 14: Complete Bootstrap JavaScript Migration

1. **Create Bootstrap compatibility layer (`backend/theme/static/js/bootstrap-compat.js`):**
```javascript
// Bootstrap compatibility layer for Tailwind migration
// This provides drop-in replacements for Bootstrap JS components

// Modal implementation
class TailwindModal {
    constructor(element) {
        this.element = typeof element === 'string' ? document.getElementById(element) : element;
        this.backdrop = null;
    }

    show() {
        if (!this.element) return;

        // Create backdrop
        this.backdrop = document.createElement('div');
        this.backdrop.className = 'modal-backdrop fixed inset-0 bg-black bg-opacity-50 z-40';
        this.backdrop.onclick = () => this.hide();
        document.body.appendChild(this.backdrop);

        // Show modal
        this.element.classList.remove('hidden');
        this.element.classList.add('flex');
        document.body.classList.add('modal-open');

        // Trigger show event
        this.element.dispatchEvent(new CustomEvent('show.bs.modal'));
    }

    hide() {
        if (!this.element) return;

        // Hide modal
        this.element.classList.add('hidden');
        this.element.classList.remove('flex');

        // Remove backdrop
        if (this.backdrop) {
            this.backdrop.remove();
            this.backdrop = null;
        }

        document.body.classList.remove('modal-open');

        // Trigger hide event
        this.element.dispatchEvent(new CustomEvent('hide.bs.modal'));
    }

    toggle() {
        if (this.element.classList.contains('hidden')) {
            this.show();
        } else {
            this.hide();
        }
    }
}

// Tab implementation
class TailwindTab {
    constructor(element) {
        this.element = element;
        this.target = document.querySelector(element.getAttribute('data-bs-target') || element.getAttribute('href'));
    }

    show() {
        if (!this.target) return;

        // Hide all tab panes in the same group
        const tabGroup = this.target.closest('.tab-content');
        if (tabGroup) {
            tabGroup.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.add('hidden');
                pane.classList.remove('active', 'show');
            });
        }

        // Show target pane
        this.target.classList.remove('hidden');
        this.target.classList.add('active', 'show');

        // Update tab button states
        const tabNav = this.element.closest('.nav-tabs, .nav-pills');
        if (tabNav) {
            tabNav.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
        }
        this.element.classList.add('active');
    }
}

// Tooltip implementation
class TailwindTooltip {
    constructor(element, options = {}) {
        this.element = element;
        this.options = {
            placement: options.placement || 'top',
            title: options.title || element.getAttribute('data-bs-title') || element.getAttribute('title'),
            ...options
        };
        this.tooltip = null;
        this.init();
    }

    init() {
        this.element.addEventListener('mouseenter', () => this.show());
        this.element.addEventListener('mouseleave', () => this.hide());
    }

    show() {
        if (this.tooltip || !this.options.title) return;

        this.tooltip = document.createElement('div');
        this.tooltip.className = 'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg pointer-events-none';
        this.tooltip.textContent = this.options.title;

        document.body.appendChild(this.tooltip);
        this.updatePosition();
    }

    hide() {
        if (this.tooltip) {
            this.tooltip.remove();
            this.tooltip = null;
        }
    }

    updatePosition() {
        if (!this.tooltip) return;

        const rect = this.element.getBoundingClientRect();
        const tooltipRect = this.tooltip.getBoundingClientRect();

        let top, left;

        switch (this.options.placement) {
            case 'top':
                top = rect.top - tooltipRect.height - 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'bottom':
                top = rect.bottom + 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.left - tooltipRect.width - 8;
                break;
            case 'right':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.right + 8;
                break;
        }

        this.tooltip.style.top = `${top}px`;
        this.tooltip.style.left = `${left}px`;
    }
}

// Create global bootstrap object for compatibility
window.bootstrap = {
    Modal: TailwindModal,
    Tab: TailwindTab,
    Tooltip: TailwindTooltip,

    // Popover placeholder (implement if needed)
    Popover: class {
        constructor(element, options) {
            console.warn('Popover not implemented in Tailwind compatibility layer');
        }
        show() {}
        hide() {}
    }
};

// Auto-initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(element => {
        new TailwindTooltip(element);
    });
});

// Handle modal dismiss buttons
document.addEventListener('click', function(event) {
    if (event.target.matches('[data-bs-dismiss="modal"]')) {
        const modal = event.target.closest('.modal');
        if (modal) {
            const modalInstance = new TailwindModal(modal);
            modalInstance.hide();
        }
    }
});

// Handle tab clicks
document.addEventListener('click', function(event) {
    if (event.target.matches('[data-bs-toggle="tab"]')) {
        event.preventDefault();
        const tabInstance = new TailwindTab(event.target);
        tabInstance.show();
    }
});
```

2. **Update scripts.html to include compatibility layer:**
```html
<!-- Add this line to backend/abstract/templates/abstract/includes/scripts.html -->
<!-- After existing scripts but before custom scripts -->
{% if USE_TAILWIND %}
    <script src="{% static 'js/bootstrap-compat.js' %}" defer></script>
{% endif %}
```

3. **Update existing modal functions for Tailwind:**
```javascript
// Update the existing modal functions in scripts.html
window.closeModal = function (modalSelector) {
    const modal = document.querySelector(modalSelector);
    if (modal) {
        if (window.USE_TAILWIND) {
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.hide();
        } else {
            // Original Bootstrap code
            modal.removeAttribute('style');
            const modal_backdrop = document.querySelector('.modal-backdrop');
            if (modal_backdrop) {
                modal_backdrop.remove();
            }
            const body = document.querySelector('body');
            if (body.classList.contains('modal-open')) {
                body.classList.remove('modal-open');
                body.removeAttribute('style');
            }
        }
    }
}
```

### Step 14.5: Update Tom-Select Styling for Tailwind

1. **Create Tom-Select Tailwind theme (`backend/theme/static/css/tom-select-tailwind.css`):**
```css
/* Tom-Select Tailwind Theme */
.ts-wrapper {
    @apply relative;
}

.ts-control {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
    @apply transition-colors duration-200;
}

.ts-control.multi .ts-control {
    @apply min-h-[2.5rem] py-1;
}

.ts-control.single .ts-control {
    @apply cursor-pointer;
}

.ts-control.disabled {
    @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}

.ts-control.focus {
    @apply ring-2 ring-primary-500 border-primary-500;
}

.ts-control.has-items .ts-control {
    @apply cursor-text;
}

.ts-dropdown {
    @apply absolute z-50 bg-white border border-gray-200 rounded-md shadow-lg mt-1;
    @apply max-h-60 overflow-auto;
}

.ts-dropdown .ts-dropdown-content {
    @apply py-1;
}

.ts-dropdown .option {
    @apply px-3 py-2 text-sm text-gray-900 cursor-pointer;
    @apply hover:bg-gray-50 hover:text-gray-900;
}

.ts-dropdown .option.active {
    @apply bg-primary-50 text-primary-600;
}

.ts-dropdown .option.selected {
    @apply bg-primary-100 text-primary-700;
}

.ts-dropdown .option.disabled {
    @apply text-gray-400 cursor-not-allowed;
    @apply hover:bg-transparent hover:text-gray-400;
}

.ts-dropdown .optgroup-header {
    @apply px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50;
}

.ts-dropdown .no-results {
    @apply px-3 py-2 text-sm text-gray-500 italic;
}

.ts-dropdown .create {
    @apply px-3 py-2 text-sm text-primary-600 cursor-pointer;
    @apply hover:bg-primary-50;
}

/* Multi-select items */
.ts-control .item {
    @apply inline-flex items-center px-2 py-1 mr-1 mb-1 text-xs font-medium;
    @apply bg-primary-100 text-primary-800 rounded;
}

.ts-control .item .remove {
    @apply ml-1 text-primary-600 hover:text-primary-800 cursor-pointer;
}

/* Input field */
.ts-control input {
    @apply border-0 outline-none bg-transparent flex-1 min-w-0;
}

/* Loading state */
.ts-control.loading::after {
    @apply absolute right-3 top-1/2 transform -translate-y-1/2;
    @apply w-4 h-4 border-2 border-gray-300 border-t-primary-600 rounded-full animate-spin;
    content: '';
}

/* RTL Support */
[dir="rtl"] .ts-control {
    @apply text-right;
}

[dir="rtl"] .ts-control .item {
    @apply ml-1 mr-0;
}

[dir="rtl"] .ts-control .item .remove {
    @apply mr-1 ml-0;
}

[dir="rtl"] .ts-dropdown {
    @apply right-0;
}
```

2. **Update Tom-Select initialization in scripts.html:**
```javascript
// Update the Tom-Select initialization for Tailwind compatibility
function initTomSelect() {
    const selects = document.querySelectorAll('select');
    selects.forEach(el => {
        if (!el.tomselect) {
            let parent = el.closest('.modal') === null ? 'body' : '';

            // Tailwind-specific configuration
            const tailwindConfig = window.USE_TAILWIND ? {
                dropdownClass: 'ts-dropdown',
                optionClass: 'option',
                controlInput: '<input class="ts-input">',
            } : {
                dropdownClass: 'dropdown-menu ts-dropdown ts-fix-width',
                optionClass: 'dropdown-item',
                controlInput: '<input>',
            };

            new TomSelect(el, {
                create: false,
                highlight: true,
                copyClassesToDropdown: false,
                dropdownParent: parent,
                ...tailwindConfig,
                render: {
                    item: function (data, escape) {
                        if (data.customProperties) {
                            return `<div><span class="${window.USE_TAILWIND ? 'text-gray-500 text-xs' : 'dropdown-item-indicator'}">${data.customProperties}</span> ${data.text}</div>`;
                        }
                        return '<div>' + data.text + '</div>';
                    },
                    option: function (data, escape) {
                        if (data.customProperties) {
                            return `<div><span class="${window.USE_TAILWIND ? 'text-gray-500 text-xs' : 'dropdown-item-indicator'}">${data.customProperties}</span> ${data.text}</div>`;
                        }
                        return '<div>' + data.text + '</div>';
                    },
                    no_results(data, escape) {
                        return `<div class="${window.USE_TAILWIND ? 'no-results' : 'no-results'}">{% translate 'لا يوجد نتائج' %}</div>`;
                    },
                    'option_create': (data, escape) => {
                        return `<div class="${window.USE_TAILWIND ? 'create' : 'create'}">{% translate 'إضافة' %} <strong>${escape(data.input)}</strong>&hellip;</div>`;
                    },
                },
            });
        }
    });
}
```

3. **Include Tom-Select CSS in head template:**
```html
<!-- Add to head_tailwind.html -->
{% if USE_TAILWIND %}
    <link href="{% static 'css/tom-select-tailwind.css' %}" rel="stylesheet"/>
{% endif %}
```

### Step 15: Production Optimization

1. **Configure production build:**
```javascript
// Update webpack.config.js for production
const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  // ... existing config
  optimization: {
    minimize: isProduction,
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: isProduction ? '[name].[contenthash].css' : '[name].css',
    }),
  ],
};
```

2. **Update Django settings for production:**
```python
# In settings.py
if not DEBUG:
    # Enable CSS compression
    COMPRESS_ENABLED = True
    COMPRESS_OFFLINE = True

    # Update static files handling
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
```

## Testing and Validation

### Step 16: Comprehensive Testing and HTMX Integration

1. **Update HTMX integration for Tailwind compatibility:**
```javascript
// Add to scripts.html - HTMX Tailwind compatibility
document.addEventListener('htmx:beforeSwap', function(event) {
    // Handle modal content swapping
    if (event.detail.target.matches('.modal-body, .modal-content')) {
        // Re-initialize Tom-Select for new content
        setTimeout(() => {
            initTomSelect();
        }, 100);

        // Re-initialize tooltips for new content
        if (window.USE_TAILWIND) {
            event.detail.target.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(element => {
                new bootstrap.Tooltip(element);
            });
        }
    }
});

// Handle HTMX loading states with Tailwind
document.addEventListener('htmx:beforeRequest', function(event) {
    const target = event.detail.elt;

    // Add loading state
    if (target.matches('button, input[type="submit"]')) {
        target.disabled = true;
        target.classList.add('opacity-50', 'cursor-not-allowed');

        // Add spinner if Tailwind is enabled
        if (window.USE_TAILWIND) {
            const spinner = document.createElement('div');
            spinner.className = 'inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin mr-2';
            spinner.setAttribute('data-htmx-spinner', '');
            target.prepend(spinner);
        }
    }
});

document.addEventListener('htmx:afterRequest', function(event) {
    const target = event.detail.elt;

    // Remove loading state
    if (target.matches('button, input[type="submit"]')) {
        target.disabled = false;
        target.classList.remove('opacity-50', 'cursor-not-allowed');

        // Remove spinner
        const spinner = target.querySelector('[data-htmx-spinner]');
        if (spinner) {
            spinner.remove();
        }
    }
});

// Handle dynamic class updates for HTMX responses
document.addEventListener('htmx:afterSwap', function(event) {
    // Update any Bootstrap classes to Tailwind equivalents in swapped content
    if (window.USE_TAILWIND) {
        const swappedElement = event.detail.target;

        // Convert common Bootstrap classes to Tailwind
        const classMap = {
            'btn-primary': 'btn btn-primary',
            'btn-secondary': 'btn btn-secondary',
            'btn-success': 'btn btn-success',
            'btn-danger': 'btn btn-danger',
            'alert-success': 'alert alert-success',
            'alert-danger': 'alert alert-danger',
            'alert-warning': 'alert alert-warning',
            'alert-info': 'alert alert-info',
        };

        Object.entries(classMap).forEach(([oldClass, newClass]) => {
            swappedElement.querySelectorAll(`.${oldClass}`).forEach(el => {
                el.classList.remove(oldClass);
                el.classList.add(...newClass.split(' '));
            });
        });
    }
});
```

2. **Create comprehensive testing checklist:**
```bash
# Create testing script
touch test-migration.sh
chmod +x test-migration.sh
```

```bash
#!/bin/bash
# test-migration.sh - Comprehensive migration testing script

echo "🧪 Starting Tailwind CSS Migration Testing..."

# Test 1: Build System
echo "📦 Testing build system..."
pnpm run build
if [ $? -eq 0 ]; then
    echo "✅ Build system working"
else
    echo "❌ Build system failed"
    exit 1
fi

# Test 2: CSS Generation
echo "🎨 Testing CSS generation..."
if [ -f "backend/theme/static/css/dist/main.css" ] && [ -f "backend/theme/static/css/dist/main-rtl.css" ]; then
    echo "✅ CSS files generated"
    echo "📊 CSS file sizes:"
    ls -lh backend/theme/static/css/dist/
else
    echo "❌ CSS files not generated"
    exit 1
fi

# Test 3: Django Settings
echo "⚙️ Testing Django configuration..."
python manage.py check --deploy
if [ $? -eq 0 ]; then
    echo "✅ Django configuration valid"
else
    echo "❌ Django configuration issues"
    exit 1
fi

# Test 4: Template Syntax
echo "📄 Testing template syntax..."
python manage.py check --tag templates
if [ $? -eq 0 ]; then
    echo "✅ Template syntax valid"
else
    echo "❌ Template syntax issues"
    exit 1
fi

echo "🎉 All tests passed!"
```

3. **Manual testing checklist:**
```markdown
## Manual Testing Checklist

### Browser Compatibility
- [ ] Chrome/Chromium (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Device Testing
- [ ] Desktop (1920x1080)
- [ ] Laptop (1366x768)
- [ ] Tablet (768x1024)
- [ ] Mobile (375x667)
- [ ] Mobile (320x568)

### Language Testing
- [ ] English (LTR) - All components
- [ ] Arabic (RTL) - All components
- [ ] Language switching functionality

### Core Functionality Testing
- [ ] **Navigation**
  - [ ] Sidebar navigation
  - [ ] Top navigation
  - [ ] Mobile menu toggle
  - [ ] Breadcrumbs
  - [ ] App switcher

- [ ] **Forms**
  - [ ] Form field styling
  - [ ] Form validation display
  - [ ] Crispy forms rendering
  - [ ] File upload fields
  - [ ] Multi-select fields (Tom-Select)

- [ ] **Modals**
  - [ ] Modal opening/closing
  - [ ] Modal backdrop clicks
  - [ ] Form modals
  - [ ] Confirmation modals
  - [ ] Image preview modals

- [ ] **Tables**
  - [ ] Table styling
  - [ ] Responsive tables
  - [ ] Table sorting
  - [ ] Table pagination
  - [ ] Row selection

- [ ] **HTMX Integration**
  - [ ] Dynamic content loading
  - [ ] Form submissions via HTMX
  - [ ] Content swapping
  - [ ] Loading states
  - [ ] Error handling

- [ ] **JavaScript Components**
  - [ ] Tooltips
  - [ ] Dropdowns
  - [ ] Tabs
  - [ ] Accordions
  - [ ] Date pickers

### Performance Testing
- [ ] Page load times (< 3 seconds)
- [ ] CSS bundle size (< 500KB)
- [ ] JavaScript execution time
- [ ] Mobile performance
- [ ] Lighthouse scores (> 90)

### Accessibility Testing
- [ ] Keyboard navigation
- [ ] Screen reader compatibility
- [ ] Color contrast ratios
- [ ] Focus indicators
- [ ] ARIA labels

### Integration Testing
- [ ] **ERP Module**
  - [ ] Invoice forms
  - [ ] Product listings
  - [ ] Customer management
  - [ ] Transaction tables

- [ ] **CRM Module**
  - [ ] Lead forms
  - [ ] Contact management
  - [ ] Event calendar
  - [ ] Activity feeds

- [ ] **HR Module**
  - [ ] Employee forms
  - [ ] Payroll tables
  - [ ] Attendance tracking
  - [ ] Leave management

- [ ] **Delivery Module**
  - [ ] Order forms
  - [ ] Tracking interface
  - [ ] Route planning
  - [ ] Status updates

- [ ] **Reporting Module**
  - [ ] Dashboard widgets
  - [ ] Chart rendering
  - [ ] Report generation
  - [ ] Export functionality
```

4. **Performance testing commands:**
```bash
# Measure CSS bundle sizes
echo "📊 CSS Bundle Analysis:"
ls -lh backend/theme/static/css/dist/
echo ""

# Compare with Bootstrap bundle sizes
echo "📊 Bootstrap Bundle Sizes (for comparison):"
ls -lh backend/theme/static/css/style.bundle*.css
echo ""

# Test page load times with curl
echo "⏱️ Testing page load times:"
time curl -s http://localhost:8000/ > /dev/null
echo ""

# Check for unused CSS (requires purgecss)
echo "🧹 Checking for unused CSS:"
npx purgecss --css backend/theme/static/css/dist/main.css --content 'backend/**/templates/**/*.html' --output temp-purged.css
echo "Original size: $(wc -c < backend/theme/static/css/dist/main.css) bytes"
echo "Purged size: $(wc -c < temp-purged.css) bytes"
rm temp-purged.css
```

## Rollback Strategy

### Step 17: Prepare Rollback Plan

1. **Environment variable rollback:**
```bash
# To rollback to Bootstrap
export USE_TAILWIND=False
# Or remove the environment variable entirely
```

2. **Quick rollback script:**
```bash
#!/bin/bash
# rollback.sh
echo "Rolling back to Bootstrap..."
export USE_TAILWIND=False
python manage.py collectstatic --noinput
echo "Rollback complete"
```

## Final Steps and Cleanup

### Step 18: Final Migration Tasks

1. **Remove Bootstrap dependencies (after full migration):**
```bash
# Remove Bootstrap-specific packages
pip uninstall crispy-bootstrap5

# Update requirements.txt
# Remove: crispy-bootstrap5==0.7
# Keep: django-crispy-forms==2.0 (for custom templates)
```

2. **Clean up old CSS files:**
```bash
# After confirming Tailwind works correctly
# Move old Bootstrap files to backup directory
mkdir -p backup/css
mv backend/theme/static/css/style.bundle.css backup/css/
mv backend/theme/static/css/style.bundle.rtl.css backup/css/
mv backend/theme/static/plugins/global/plugins.bundle.css backup/css/
mv backend/theme/static/plugins/global/plugins.bundle.rtl.css backup/css/
```

3. **Update documentation:**
```bash
# Update README.md with new build instructions
# Update developer documentation
# Create component style guide
```

## Technical Implementation Details

### Current Project Structure Analysis
```
iCRM/
├── backend/
│   ├── abstract/templates/abstract/
│   │   ├── _base.html (main template)
│   │   └── includes/
│   │       ├── head.html (CSS includes)
│   │       ├── sidebar.html (navigation)
│   │       └── navbar.html (top bar)
│   ├── theme/static/
│   │   ├── css/
│   │   │   ├── style.bundle.css (Bootstrap)
│   │   │   ├── style.bundle.rtl.css (Bootstrap RTL)
│   │   │   └── main.css (custom styles)
│   │   └── libs/bootstrap/ (Bootstrap 5.3.0-alpha3)
│   └── settings.py (Django configuration)
├── requirements.txt (Python dependencies)
└── [package.json will be created]
```

### Migration Architecture
```
New Structure:
├── frontend/src/
│   ├── main.css (Tailwind entry point)
│   └── main-rtl.css (Tailwind RTL)
├── backend/theme/static/css/dist/
│   ├── main.css (compiled Tailwind)
│   └── main-rtl.css (compiled Tailwind RTL)
├── tailwind.config.js
├── postcss.config.js
├── webpack.config.js
└── package.json
```

### Build System Configuration

#### Complete Package.json
```json
{
  "name": "icrm-frontend",
  "version": "1.0.0",
  "description": "Frontend build system for iCRM Tailwind migration",
  "scripts": {
    "build": "NODE_ENV=production webpack --mode=production",
    "dev": "webpack --mode=development --watch",
    "build-css": "tailwindcss -i ./frontend/src/main.css -o ./backend/theme/static/css/dist/main.css --watch",
    "build-css-rtl": "tailwindcss -i ./frontend/src/main-rtl.css -o ./backend/theme/static/css/dist/main-rtl.css --watch",
    "clean": "rm -rf backend/theme/static/css/dist/*"
  },
  "devDependencies": {
    "tailwindcss": "^3.4.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.32",
    "webpack": "^5.89.0",
    "webpack-cli": "^5.1.4",
    "css-loader": "^6.8.1",
    "mini-css-extract-plugin": "^2.7.6",
    "postcss-loader": "^7.3.3",
    "@tailwindcss/forms": "^0.5.7",
    "@tailwindcss/typography": "^0.5.10",
    "@tailwindcss/aspect-ratio": "^0.4.2"
  },
  "engines": {
    "node": ">=18.0.0",
    "pnpm": ">=8.0.0"
  }
}
```

#### Updated Django Settings
```python
# backend/settings.py additions/modifications

# Add feature flag
USE_TAILWIND = get_setting("USE_TAILWIND", False, cast=bool)

# Update INSTALLED_APPS
INSTALLED_APPS = [
    "django_multitenant",
    # ... existing apps ...
    "crispy_forms",
    "crispy_bootstrap5",  # Keep during transition
    # ... other apps ...
    "compressor",  # Add for CSS compression
    # ... rest of apps ...
]

# Static files configuration
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'compressor.finders.CompressorFinder',
]

# Compressor settings
COMPRESS_ENABLED = not DEBUG
COMPRESS_CSS_FILTERS = [
    'compressor.filters.css_default.CssAbsoluteFilter',
    'compressor.filters.cssmin.rCSSMinFilter',
]

# Crispy forms configuration
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Override for Tailwind when enabled
if USE_TAILWIND:
    CRISPY_TEMPLATE_PACK = "tailwind"
    CRISPY_ALLOWED_TEMPLATE_PACKS = "tailwind"

# Template configuration with context processor
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
                'backend.abstract.context_processors.ui_framework',
            ],
        },
    },
]
```

### Component Migration Examples

#### Button Component Migration
```html
<!-- Before (Bootstrap) -->
<button class="btn btn-primary btn-sm">
    Save Changes
</button>

<!-- After (Tailwind with component class) -->
<button class="btn btn-primary btn-sm">
    Save Changes
</button>
<!-- Uses the .btn, .btn-primary, .btn-sm classes defined in main.css -->

<!-- Or pure Tailwind classes -->
<button class="bg-primary hover:bg-primary-600 text-white font-medium py-1.5 px-3 rounded-md text-xs transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
    Save Changes
</button>
```

#### Card Component Migration
```html
<!-- Before (Bootstrap) -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">Card Title</h5>
    </div>
    <div class="card-body">
        <p class="card-text">Card content</p>
    </div>
</div>

<!-- After (Tailwind with component classes) -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">Card Title</h5>
    </div>
    <div class="card-body">
        <p class="card-text">Card content</p>
    </div>
</div>
<!-- Uses the .card, .card-header, .card-body, .card-title classes defined in main.css -->
```

#### Form Component Migration
```html
<!-- Before (Bootstrap with crispy forms) -->
{% load crispy_forms_tags %}
{% crispy form %}

<!-- After (Tailwind with custom crispy templates) -->
{% load crispy_forms_tags %}
{% crispy form %}
<!-- Uses custom Tailwind crispy form templates -->

<!-- Or manual form field -->
<!-- Before (Bootstrap) -->
<div class="mb-3">
    <label for="email" class="form-label">Email</label>
    <input type="email" class="form-control" id="email" name="email">
</div>

<!-- After (Tailwind) -->
<div class="mb-4">
    <label for="email" class="form-label">Email</label>
    <input type="email" class="form-control" id="email" name="email">
</div>
<!-- Uses the .form-label and .form-control classes defined in main.css -->
```

#### Grid System Migration
```html
<!-- Before (Bootstrap) -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-6">Column 1</div>
        <div class="col-md-6">Column 2</div>
    </div>
</div>

<!-- After (Tailwind) -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>Column 1</div>
        <div>Column 2</div>
    </div>
</div>
```

#### Modal Component Migration
```html
<!-- Before (Bootstrap) -->
<div class="modal fade" id="exampleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modal title</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Modal content
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>

<!-- After (Tailwind with component classes) -->
<div class="modal hidden" id="exampleModal">
    <div class="modal-backdrop" onclick="hideModal('exampleModal')"></div>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="text-lg font-semibold">Modal title</h5>
                <button type="button" onclick="hideModal('exampleModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                Modal content
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('exampleModal')">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>
```

## Risk Assessment and Mitigation

### High-Risk Areas
1. **Form Rendering**: Complex crispy forms layouts may require significant template rework
2. **JavaScript Dependencies**: Bootstrap JS components (modals, dropdowns, tooltips) need replacement
3. **Third-Party Integrations**: Tom-Select, CKEditor, Leaflet may have Bootstrap styling dependencies
4. **RTL Support**: Arabic language support requires careful Tailwind RTL configuration
5. **HTMX Integration**: Dynamic content loading may break with class changes

### Mitigation Strategies
1. **Feature Flag System**: Use `USE_TAILWIND` environment variable for gradual rollout
2. **Component Class Mapping**: Create Tailwind component classes that match Bootstrap class names
3. **Parallel Templates**: Maintain both Bootstrap and Tailwind templates during transition
4. **Comprehensive Testing**: Test both LTR and RTL layouts extensively
5. **Rollback Plan**: Quick environment variable change to revert to Bootstrap

### Specific Risk Mitigation

#### JavaScript Component Replacement
```javascript
// Create compatibility layer for existing Bootstrap JS calls
window.bootstrap = {
    Modal: class {
        constructor(element) {
            this.element = element;
        }
        show() {
            showModal(this.element.id);
        }
        hide() {
            hideModal(this.element.id);
        }
    },
    Popover: class {
        constructor(element, options) {
            // Implement with Tailwind-compatible popover
        }
    }
};
```

## Success Metrics

### Performance Metrics
- [ ] CSS bundle size reduction (target: 40-60% smaller than current Bootstrap bundle)
- [ ] Page load time improvement (target: 10-20% faster)
- [ ] First Contentful Paint (FCP) improvement
- [ ] Cumulative Layout Shift (CLS) reduction

### Development Metrics
- [ ] Component development time reduction (target: 30% faster)
- [ ] Design consistency improvement (measured by design system compliance)
- [ ] Developer satisfaction scores (survey-based)
- [ ] Code maintainability metrics (reduced CSS specificity conflicts)

### User Experience Metrics
- [ ] User interface consistency across all modules
- [ ] Accessibility compliance (WCAG 2.1 AA) - maintain current level
- [ ] Cross-browser compatibility (Chrome, Firefox, Safari, Edge)
- [ ] Mobile responsiveness improvement (better touch targets, spacing)

## Resource Requirements

### Team Requirements
- **Frontend Developer**: 1 full-time (lead migration effort, Tailwind expertise)
- **Backend Developer**: 0.5 full-time (Django template integration)
- **UI/UX Designer**: 0.25 full-time (design system consistency review)
- **QA Engineer**: 0.5 full-time (comprehensive testing across browsers/devices)

### Timeline (Revised for Project Complexity)
- **Phase 1 (Setup)**: 1-2 weeks
- **Phase 2 (Core Infrastructure)**: 2-3 weeks
- **Phase 3 (Template Migration)**: 4-6 weeks
- **Phase 4 (JavaScript Integration)**: 2-3 weeks
- **Phase 5 (Testing & Optimization)**: 2-3 weeks
- **Total Duration**: 11-17 weeks
- **Buffer Time**: 3-4 weeks (recommended for complex enterprise system)

### Budget Considerations
- Development time: ~4-5 person-months
- Testing and QA: ~1.5 person-months
- Training and documentation: ~0.5 person-month
- **Total Estimated Effort**: ~6-7 person-months

## Post-Migration Maintenance

### Immediate Post-Migration (First Month)
1. **Bug Fixes**: Address any issues discovered in production
2. **Performance Monitoring**: Monitor CSS load times and bundle sizes
3. **User Feedback**: Collect and address user experience feedback
4. **Documentation Updates**: Complete component library documentation

### Ongoing Tasks (Monthly)
1. **Component Library Maintenance**: Regular updates and new component additions
2. **Performance Monitoring**: Continuous monitoring of CSS performance metrics
3. **Design System Evolution**: Regular updates to maintain design consistency
4. **Developer Training**: Ongoing education on Tailwind best practices

### Long-term Benefits (6+ Months)
1. **Faster Development**: Utility-first approach speeds up new feature development
2. **Better Performance**: Smaller CSS bundles and better optimization
3. **Improved Maintainability**: More predictable and modular styling approach
4. **Enhanced Developer Experience**: Better tooling and development workflow
5. **Future-Proof Architecture**: Modern CSS architecture aligned with industry standards

## Conclusion

This comprehensive migration plan provides a structured, step-by-step approach to transitioning the iCRM system from Bootstrap 5.3.0-alpha3 to Tailwind CSS while minimizing risks and ensuring system stability.

**Key Success Factors:**
- **Gradual Migration**: Feature flag system allows for safe, incremental rollout
- **Component Compatibility**: Tailwind component classes maintain Bootstrap class names
- **Comprehensive Testing**: Extensive testing across browsers, devices, and languages
- **Risk Mitigation**: Multiple fallback strategies and rollback options

The migration will result in a more maintainable, performant, and developer-friendly CSS architecture that:
- Reduces CSS bundle size by 40-60%
- Improves development velocity by 30%
- Provides better long-term maintainability
- Aligns with modern web development practices
- Maintains full RTL support for Arabic language
- Preserves all existing functionality during transition

This plan accounts for the specific complexities of the iCRM system including multi-language support, complex form layouts, HTMX integration, and enterprise-level requirements.
