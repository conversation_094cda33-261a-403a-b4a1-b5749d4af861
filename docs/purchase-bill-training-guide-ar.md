# دليل تدريب إدارة فواتير الشراء
## دليل المستخدم الشامل لنظام فواتير الشراء في تخطيط موارد المؤسسات (ERP)

### جدول المحتويات
1. [مقدمة](#مقدمة)
2. [نظرة عامة على النظام](#نظرة-عامة-على-النظام)
3. [البدء](#البدء)
4. [دورة حياة فاتورة الشراء](#دورة-حياة-فاتورة-الشراء)
5. [الميزات الأساسية](#الميزات-الأساسية)
6. [سير العمل المتقدم](#سير-العمل-المتقدم)
7. [أفضل الممارسات](#أفضل-الممارسات)
8. [استكشاف الأخطاء وإصلاحها](#استكشاف-الأخطاء-وإصلاحها)

---

## مقدمة

مرحبًا بكم في دليل التدريب الشامل لنظام إدارة فواتير الشراء في تخطيط موارد المؤسسات (ERP). سيرشدك هذا الدليل خلال كل جانب من جوانب إدارة فواتير الشراء، من الإنشاء الأساسي إلى سير العمل المتقدم.

**ما ستتعلمه:**
- كيفية إنشاء وإدارة فواتير الشراء بكفاءة
- فهم دورة حياة فاتورة الشراء الكاملة
- الميزات المتقدمة مثل مدفوعات الموردين، وتسجيل المصروفات، والعمليات المجمعة
- التكامل مع وحدات المخزون والمحاسبة
- أفضل الممارسات للدقة والامتثال

---

## نظرة عامة على النظام

### نظرة عامة على البنية

```mermaid
graph TD
    A[إنشاء فاتورة الشراء] --> B[تعديل الفاتورة]
    B --> C[التزام الفاتورة]
    C --> D[ترحيل الفاتورة]
    D --> E[معالجة الدفع]
    E --> F[الإكمال/الأرشفة]
    
    B --> G[إشعارات الخصم]
    B --> H[مدفوعات الموردين]
    C --> I[العمليات المجمعة]
    D --> J[تسجيل المصروفات]
    K[تكامل المخزون] --> C
    L[تكامل المحاسبة] --> D
```

### المكونات الرئيسية

1. **نموذج الفاتورة**: الكيان الأساسي الذي يحتوي على جميع بيانات فاتورة الشراء
2. **الأنشطة**: بنود فردية داخل فاتورة الشراء
3. **المدفوعات**: سجلات الدفع المرتبطة بفواتير الشراء
4. **إشعارات الخصم**: مستندات التعديل للمرتجعات/التصحيحات
5. **إدارة الموردين**: تتبع علاقة البائع والرصيد

---

## البدء

### الوصول إلى وحدة فواتير الشراء

**مسار التنقل:**
```
لوحة القيادة ← المشتريات ← الفواتير ← قائمة الفواتير
```

### الإعداد الأولي

1. **الأذونات المطلوبة للمستخدم:**
   - `view_bill` - وصول عرض أساسي
   - `add_bill` - إنشاء فواتير شراء جديدة
   - `change_bill` - تعديل فواتير الشراء الموجودة
   - `delete_bill` - إلغاء/حذف فواتير الشراء
   - `post_bill` - ترحيل الفواتير إلى المحاسبة
   - `bill_payment` - معالجة مدفوعات الموردين

2. **الإعدادات الافتراضية:**
   - العملة الافتراضية (قابلة للتكوين لكل مستأجر)
   - موقع المخزون الافتراضي
   - مركز التكلفة الافتراضي
   - شروط المورد الافتراضية

---

## دورة حياة فاتورة الشراء

### سير العمل الكامل

```mermaid
stateDiagram-v2
    [*] --> مسودة: إنشاء فاتورة شراء
    مسودة --> ملتزم: التزام البنود
    ملتزم --> مرحل: ترحيل إلى المحاسبة
    مرحل --> مدفوع_جزئيا: دفع جزئي
    مرحل --> مدفوع: دفع كامل
    مدفوع_جزئيا --> مدفوع: الدفع المتبقي
    مدفوع --> [*]
    
    مسودة --> ملغاة: إلغاء
    ملتزم --> ملغاة: إلغاء
    مرحل --> ملغاة: إلغاء مع عكس
```

### انتقالات الحالة

| الحالة | الوصف | الإجراءات المتاحة |
|-------|-------------|-------------------|
| مسودة | الإنشاء الأولي | تعديل، إضافة بنود، حذف |
| ملتزم | البنود محجوزة للشراء | إلغاء الالتزام، ترحيل |
| مرحل | تم إنشاء قيد محاسبي | دفع، إشعار خصم |
| مدفوع | تمت التسوية بالكامل | عرض، أرشفة |
| ملغاة | تم الإلغاء | عرض السجل |

---

## الميزات الأساسية

### 1. إنشاء فواتير الشراء

#### الطريقة 1: إنشاء سريع
```python
# انتقل إلى: المشتريات ← الفواتير ← إضافة فاتورة
# الحقول المطلوبة:
- المورد (اختر من القائمة المنسدلة)
- العملة (تعبئة تلقائية من الإعدادات الافتراضية)
- البنود (المنتجات/الخدمات المراد شراؤها)
- الكميات
- الأسعار
- تواريخ الشراء
```

#### الطريقة 2: خاصة بالمورد
```
1. انتقل إلى ملف تعريف المورد
2. انقر على "إنشاء فاتورة شراء"
3. يتم تعبئة تفاصيل المورد تلقائيًا
4. أضف البنود واحفظ
```

### 2. تعديل فاتورة الشراء

**مكونات شاشة التعديل:**
- **المعلومات الأساسية**: المورد، التاريخ، المرجع، أمر الشراء
- **بنود السطر**: المنتجات، الكميات، الأسعار، الضرائب
- **شروط الدفع**: تاريخ الاستحقاق، طريقة الدفع، شروط الخصم
- **الملاحظات**: ملاحظات داخلية، مذكرة المورد

**ميزات التعديل المضمن:**
- انقر على أي حقل للتعديل
- حسابات في الوقت الفعلي
- وظيفة الحفظ التلقائي

### 3. إدارة البنود

#### إضافة بنود
1. **طريقة البحث**: اكتب اسم المنتج/الرمز
2. **مسح الباركود**: استخدم ماسح الباركود
3. **إضافة سريعة**: قوالب محددة مسبقًا
4. **استيراد**: استيراد مجمع من ملف CSV

#### أنواع البنود المدعومة:
- **المنتجات**: سلع مادية
- **الخدمات**: عروض غير مادية
- **المصروفات**: تكاليف قابلة للفوترة
- **الأصول**: مشتريات رأسمالية

### 4. التسعير والخصومات

**خيارات التسعير:**
- تسعير ثابت
- تسعير خاص بالمورد
- تسعير على أساس الحجم
- خصومات ترويجية
- خصومات الدفع المبكر

**تطبيق الخصم:**
```
- خصومات بنود السطر
- خصومات على مستوى الفاتورة
- حسومات الموردين
- خصومات التسوية
```

---

## سير العمل المتقدم

### 1. معالجة مدفوعات الموردين

#### إنشاء المدفوعات
```mermaid
flowchart LR
    A[اختر فاتورة شراء] --> B[انقر على دفع]
    B --> C[أدخل المبلغ]
    C --> D[اختر طريقة الدفع]
    D --> E[سجل الدفع]
    E --> F[تحديث الرصيد تلقائيًا]
```

#### طرق الدفع
- **نقداً**: معاملات نقدية مباشرة
- **تحويل بنكي**: تحويلات إلكترونية
- **بطاقة ائتمان**: مدفوعات بالبطاقة
- **شيك**: مدفوعات بشيك ورقي
- **رصيد المورد**: تطبيق أرصدة موجودة
- **شطب دين**: استخدام رصيد المورد

#### المدفوعات الجزئية
```
سيناريو: فاتورة شراء بقيمة 1000 دولار
- الدفعة 1: 400 دولار (الرصيد: 600 دولار)
- الدفعة 2: 600 دولار (الرصيد: 0 دولار)
- يتتبع النظام جميع المدفوعات مع الطوابع الزمنية
```

### 2. إدارة إشعارات الخصم

#### إنشاء إشعارات الخصم
**متى تستخدم:**
- مرتجعات المنتجات للمورد
- إلغاء الخدمات
- تصحيحات التسعير
- تعديلات الفواتير الزائدة

**العملية:**
1. اختر فاتورة الشراء الأصلية
2. اختر البنود المراد خصمها
3. حدد مبلغ الخصم
4. أنشئ إشعار الخصم
5. طبق على حساب المورد

### 3. تسجيل المصروفات

#### إنشاء المصروفات المباشرة
```
1. الوصول: فاتورة الشراء ← المزيد من الإجراءات ← إنشاء مصروف
2. التكوين:
   - فئة المصروف
   - تخصيص مركز التكلفة
   - الآثار الضريبية
   - سير عمل الموافقة
```

### 4. العمليات المجمعة

#### ترحيل الفواتير المجمعة
```
1. اختر فواتير متعددة (مربعات الاختيار)
2. انقر على "إجراءات مجمعة" ← "ترحيل الفواتير"
3. يعالج النظام جميع الفواتير المحددة
4. تظهر رسالة تأكيد
```

#### إنشاء الدفع المجمع
```
1. تصفية الفواتير حسب المورد
2. اختر الفواتير غير المدفوعة
3. أنشئ دفعة واحدة لفواتير متعددة
4. يخصص النظام الدفعة تلقائيًا
```

---

## ميزات التكامل

### 1. تكامل المخزون

**تحديثات في الوقت الفعلي:**
- تعديل مستويات المخزون عند الالتزام
- عكس عند الإلغاء
- التكامل مع تقارير المخزون
- تنبيهات تلقائية لنقطة إعادة الطلب

### 2. تكامل المحاسبة

**قيود تلقائية:**
```
ترحيل الفاتورة:
- مدين: حساب المخزون/المصروفات
- دائن: حسابات الدفع
- مدين: ضريبة مستحقة (إن وجدت)

تسجيل الدفع:
- مدين: حسابات الدفع
- دائن: نقد/بنك
```

### 3. إدارة الموردين

**تكامل بوابة الموردين:**
- عرض سجل الشراء
- تتبع حالة الدفع
- تنزيل أوامر الشراء
- إرسال الفواتير إلكترونيًا

---

## أفضل الممارسات

### 1. دقة البيانات

**قائمة التحقق من الصحة:**
- [ ] معلومات المورد صحيحة
- [ ] رموز البنود دقيقة
- [ ] الأسعار محدثة
- [ ] حسابات الضرائب تم التحقق منها
- [ ] شروط الدفع واضحة
- [ ] مراجع أمر الشراء

### 2. كفاءة سير العمل

**نصائح لتوفير الوقت:**
- استخدم قوالب الفواتير للموردين المتكررين
- إعداد شروط الدفع الافتراضية
- تمكين الحفظ التلقائي
- استخدم العمليات المجمعة للمهام المتشابهة
- إعداد تسعير خاص بالمورد

### 3. الأمان

**التحكم في الوصول:**
- أذونات قائمة على الأدوار
- سجلات التدقيق لجميع التغييرات
- معالجة الدفع الآمنة
- تشفير البيانات
- التحقق من المورد

### 4. الامتثال

**المتطلبات التنظيمية:**
- دقة حساب الضرائب
- تسلسل ترقيم فاتورة الشراء
- إكمال الحقول المطلوبة
- سياسات الاحتفاظ
- فحوصات امتثال المورد

---

## استكشاف الأخطاء وإصلاحها

### المشكلات الشائعة والحلول

#### المشكلة 1: الفاتورة لا ترحل
**الأعراض:** رسالة خطأ عند الترحيل
**الحلول:**
- تحقق مما إذا كانت جميع الحقول المطلوبة مملوءة
- تحقق من حد ائتمان المورد
- تأكد من أن البنود لها أسعار صالحة
- تحقق من توفر المخزون للمنتجات
- تحقق من أن الفترة المحاسبية مفتوحة

#### المشكلة 2: الدفع لا يسجل
**الأعراض:** مبلغ الدفع لا يقلل الرصيد
**الحلول:**
- تحقق من إعداد طريقة الدفع
- تحقق من مطابقة العملة
- تأكد من ترحيل الفاتورة
- راجع أذونات المستخدم
- تحقق من حالة حساب المورد

#### المشكلة 3: حسابات الضرائب غير صحيحة
**الأعراض:** مبالغ الضرائب تبدو خاطئة
**الحلول:**
- تحقق من أن أسعار الضرائب محدثة
- تحقق من حالة ضريبة المورد
- راجع فئات ضريبة البنود
- قم بتحديث قواعد الضرائب إذا لزم الأمر
- تحقق من إعدادات الولاية القضائية

### الحصول على المساعدة

**موارد الدعم:**
- دليل المستخدم (هذا الدليل)
- دروس الفيديو
- مكتب المساعدة: <EMAIL>
- جلسات التدريب: ندوات شهرية عبر الإنترنت

---

## مرجع سريع

### اختصارات لوحة المفاتيح

| الإجراء | الاختصار |
|--------|----------|
| فاتورة شراء جديدة | Ctrl+N |
| حفظ الفاتورة | Ctrl+S |
| طباعة الفاتورة | Ctrl+P |
| البحث عن الفواتير | Ctrl+F |
| دفع سريع | Ctrl+Shift+P |

### المهام الشائعة

| المهمة | مسار التنقل |
|------|-----------------|
| عرض جميع فواتير الشراء | المشتريات ← الفواتير ← قائمة |
| إنشاء فاتورة شراء | المشتريات ← الفواتير ← إضافة |
| معالجة دفع المورد | الفاتورة ← الإجراءات ← دفع |
| إنشاء تقرير | التقارير ← المشتريات ← الفواتير |
| تصدير البيانات | قائمة ← تصدير ← اختر التنسيق |

---

## الخلاصة

يغطي هذا الدليل الشامل جميع جوانب نظام إدارة فواتير الشراء. ستضمن الممارسة المنتظمة لهذه الميزات معالجة فواتير الشراء بكفاءة ودقة.

**الخطوات التالية:**
1. تدرب على إنشاء فواتير شراء اختبارية
2. استكشف الميزات المتقدمة مثل إشعارات الخصم
3. قم بإعداد التكامل مع نظام المحاسبة الخاص بك
4. جدولة تحديثات تدريب منتظمة

**تذكر:** مفتاح إتقان نظام فواتير الشراء هو الممارسة المستمرة والبقاء على اطلاع دائم بالميزات الجديدة وأفضل الممارسات.

---

*للحصول على دعم إضافي أو جلسات تدريب مخصصة، اتصل بفريق التدريب لدينا على <EMAIL>*