# دليل تدريب إدارة الفواتير
## دليل المستخدم الشامل لنظام فواتير إدارة الموارد المؤسسية

### جدول المحتويات
1. [مقدمة](#مقدمة)
2. [نظرة عامة على النظام](#نظرة-عامة-على-النظام)
3. [البداية](#البداية)
4. [دورة حياة الفاتورة](#دورة-حياة-الفاتورة)
5. [الميزات الأساسية](#الميزات-الأساسية)
6. [سير العمل المتقدمة](#سير-العمل-المتقدمة)
7. [أفضل الممارسات](#أفضل-الممارسات)
8. [حل المشكلات](#حل-المشكلات)

---

## مقدمة

مرحباً بكم في الدليل التدريبي الشامل لنظام إدارة الفواتير في إدارة الموارد المؤسسية. سيرشدكم هذا الدليل عبر كل جوانب إدارة الفواتير، من الإنشاء الأساسي إلى سير العمل المتقدمة.

**ما ستتعلمه:**
- كيفية إنشاء وإدارة الفواتير بكفاءة
- فهم دورة حياة الفاتورة الكاملة
- الميزات المتقدمة مثل الأقساط، والإشعارات الدائنة، والعمليات الجماعية
- التكامل مع وحدات النظام الأخرى
- أفضل الممارسات للدقة والامتثال

---

## نظرة عامة على النظام

### نظرة عامة على البنية

```mermaid
graph TD
    A[إنشاء الفاتورة] --> B[تحرير الفاتورة]
    B --> C[التزام الفاتورة]
    C --> D[ترحيل الفاتورة]
    D --> E[معالجة الدفع]
    E --> F[الإكمال/الأرشيف]
    
    B --> G[الإشعارات الدائنة]
    B --> H[الأقساط]
    C --> I[العمليات الجماعية]
    D --> J[تسجيل المصاريف]
```

### المكونات الرئيسية

1. **نموذج الفاتورة**: الكيان الأساسي الذي يحتوي على جميع بيانات الفاتورة
2. **الأنشطة**: البنود الفردية داخل الفاتورة
3. **المدفوعات**: سجلات الدفع المرتبطة بالفواتير
4. **الإشعارات الدائنة**: مستندات الاسترداد/التعديل
5. **الأقساط**: إدارة جدول الدفع

---

## البداية

### الوصول إلى وحدة الفواتير

**مسار التنقل:**
```
لوحة التحكم → المبيعات → الفواتير → قائمة الفواتير
```

### الإعداد الأولي

1. **الأذونات المطلوبة للمستخدم:**
   - `view_invoice` - الوصول الأساسي للعرض
   - `add_invoice` - إنشاء فواتير جديدة
   - `change_invoice` - تعديل الفواتير الموجودة
   - `delete_invoice` - إلغاء/حذف الفواتير
   - `post_invoice` - ترحيل الفواتير للمحاسبة
   - `invoice_payment` - معالجة المدفوعات

2. **الإعدادات الافتراضية:**
   - العملة الافتراضية (قابلة للتكوين لكل مستأجر)
   - موقع المخزون الافتراضي
   - مركز التكلفة الافتراضي

---

## دورة حياة الفاتورة

### سير العمل الكامل

```mermaid
stateDiagram-v2
    [*] --> مسودة: إنشاء فاتورة
    مسودة --> ملتزمة: التزام البنود
    ملتزمة --> مرحلة: الترحيل للمحاسبة
    مرحلة --> مدفوعة_جزئياً: دفع جزئي
    مرحلة --> مدفوعة: دفع كامل
    مدفوعة_جزئياً --> مدفوعة: الدفع المتبقي
    مدفوعة --> [*]
    
    مسودة --> ملغاة: الإلغاء
    ملتزمة --> ملغاة: الإلغاء
    مرحلة --> ملغاة: الإلغاء مع العكس
```

### انتقالات الحالة

| الحالة | الوصف | الإجراءات المتاحة |
|--------|-------|-------------------|
| مسودة | الإنشاء الأولي | التحرير، إضافة البنود، الحذف |
| ملتزمة | البنود محجوزة | إلغاء الالتزام، الترحيل |
| مرحلة | تم إنشاء قيد محاسبي | الدفع، الإشعار الدائن |
| مدفوعة | تم التسوية بالكامل | العرض، الأرشيف |
| ملغاة | تم الإبطال | عرض السجل |

---

## الميزات الأساسية

### 1. إنشاء الفواتير

#### الطريقة 1: الإنشاء السريع
```python
# التنقل إلى: المبيعات → الفواتير → إضافة فاتورة
# الحقول المطلوبة:
- العميل (اختيار من القائمة المنسدلة)
- العملة (يتم ملؤها تلقائياً من الإعدادات)
- البنود (المنتجات/الخدمات)
- الكميات
- الأسعار
```

#### الطريقة 2: خاص بالعميل
```
1. انتقل إلى ملف العميل
2. انقر "إنشاء فاتورة"
3. تُملأ تفاصيل العميل تلقائياً
4. أضف البنود واحفظ
```

### 2. تحرير الفاتورة

**مكونات شاشة التحرير:**
- **المعلومات الأساسية**: العميل، التاريخ، المرجع
- **بنود الفاتورة**: المنتجات، الكميات، الأسعار
- **شروط الدفع**: تاريخ الاستحقاق، طريقة الدفع
- **الملاحظات**: ملاحظات داخلية، مذكرة للعميل

**ميزات التحرير المضمنة:**
- النقر على أي حقل للتحرير
- الحسابات في الوقت الفعلي
- وظيفة الحفظ التلقائي

### 3. إدارة البنود

#### إضافة البنود
1. **طريقة البحث**: اكتب اسم المنتج/الرمز
2. **مسح الباركود**: استخدم ماسح الباركود
3. **الإضافة السريعة**: القوالب المحددة مسبقاً

#### أنواع البنود المدعومة:
- **المنتجات**: السلع المادية
- **الخدمات**: العروض غير المادية
- **المجموعات**: المنتجات المجمعة
- **المصاريف**: التكاليف القابلة للفوترة

### 4. التسعير والخصومات

**خيارات التسعير:**
- التسعير الثابت
- الخصومات النسبية
- التسعير القائم على الكمية
- التسعير الخاص بالعميل

**تطبيق الخصم:**
```
- خصومات بند الفاتورة
- خصومات مستوى الفاتورة
- الرموز الترويجية
- خصومات الولاء
```

---

## سير العمل المتقدمة

### 1. معالجة الدفع

#### إنشاء المدفوعات
```mermaid
flowchart LR
    A[اختيار الفاتورة] --> B[النقر على الدفع]
    B --> C[إدخال المبلغ]
    C --> D[اختيار طريقة الدفع]
    D --> E[تسجيل الدفع]
    E --> F[التحديث التلقائي للرصيد]
```

#### طرق الدفع
- **نقداً**: المعاملات النقدية المباشرة
- **التحويل المصرفي**: التحويلات الإلكترونية
- **بطاقة الائتمان**: مدفوعات البطاقات
- **الشيك**: مدفوعات الشيكات الورقية
- **مذكرة الدائن**: تطبيق الأرصدة الموجودة

#### المدفوعات الجزئية
```
السيناريو: فاتورة بقيمة 1000$
- الدفع 1: 400$ (الرصيد: 600$)
- الدفع 2: 600$ (الرصيد: 0$)
- النظام يتتبع جميع المدفوعات مع الطوابع الزمنية
```

### 2. إدارة الأقساط

#### إعداد الأقساط
1. **الوصول**: الفاتورة → المزيد من الإجراءات → إنشاء أقساط
2. **التكوين**:
   - عدد الأقساط
   - المبلغ لكل قسط
   - تواريخ الاستحقاق
   - تذكيرات الدفع

#### تتبع الأقساط
```python
# مثال جدول الأقساط:
القسط 1: 250$ مستحق 2024-01-15
القسط 2: 250$ مستحق 2024-02-15
القسط 3: 250$ مستحق 2024-03-15
القسط 4: 250$ مستحق 2024-04-15
```

### 3. الإشعارات الدائنة والمستردات

#### إنشاء الإشعارات الدائنة
**متى تستخدم:**
- إرجاع المنتجات
- إلغاء الخدمات
- تصحيحات التسعير
- المدفوعات الزائدة

**العملية:**
1. اختر الفاتورة الأصلية
2. اختر البنود المراد إرجاعها
3. حدد مبلغ الإرجاع
4. أنشئ إشعار دائن
5. طبقه على حساب العميل

### 4. العمليات الجماعية

#### ترحيل الفواتير بالجملة
```
1. حدد فواتير متعددة (مربعات الاختيار)
2. انقر "إجراءات جماعية" → "ترحيل الفواتير"
3. يقوم النظام بمعالجة جميع الفواتير المحددة
4. تظهر رسالة تأكيد
```

#### إنشاء المدفوعات بالجملة
```
1. قم بتصفية الفواتير حسب العميل
2. حدد الفواتير غير المدفوعة
3. أنشئ دفعة واحدة لفواتير متعددة
4. يقوم النظام بتخصيص الدفعة تلقائياً
```

---

## ميزات التكامل

### 1. تكامل المخزون

**تحديثات في الوقت الفعلي:**
- مستويات المخزون تتعدل عند الالتزام
- عكس العملية عند الإلغاء
- التكامل مع تقارير المخزون

### 2. التكامل المحاسبي

**إدخالات تلقائية:**
```
ترحيل الفاتورة:
- مدين: حسابات القبض
- دائن: إيرادات المبيعات
- دائن: ضرائب مستحقة الدفع

تسجيل الدفع:
- مدين: نقد/بنك
- دائن: حسابات القبض
```

### 3. إدارة العملاء

**تكامل بوابة العملاء:**
- عرض سجل الفواتير
- سداد المدفوعات عبر الإنترنت
- تنزيل الفواتير
- تتبع حالة الدفع

---

## أفضل الممارسات

### 1. دقة البيانات

**قائمة التحقق من الصحة:**
- [ ] معلومات العميل صحيحة
- [ ] رموز البنود دقيقة
- [ ] الأسعار محدثة
- [ ] حسابات الضرائب تم التحقق منها
- [ ] شروط الدفع واضحة

### 2. كفاءة سير العمل

**نصائح لتوفير الوقت:**
- استخدم قوالب الفواتير للعملاء المتكررين
- قم بإعداد شروط الدفع الافتراضية
- قم بتمكين الحفظ التلقائي
- استخدم العمليات الجماعية للمهام المتشابهة

### 3. الأمان

**التحكم في الوصول:**
- أذونات قائمة على الأدوار
- سجلات التدقيق لجميع التغييرات
- معالجة الدفع الآمنة
- تشفير البيانات

### 4. الامتثال

**المتطلبات التنظيمية:**
- دقة حساب الضرائب
- تسلسل ترقيم الفواتير
- إكمال الحقول المطلوبة
- سياسات الاحتفاظ بالبيانات

---

## حل المشكلات

### المشكلات الشائعة والحلول

#### المشكلة 1: الفاتورة لا ترحل
**الأعراض:** رسالة خطأ عند الترحيل
**الحلول:**
- تحقق مما إذا كانت جميع الحقول المطلوبة مملوءة
- تحقق من حد ائتمان العميل
- تأكد من أن البنود لها أسعار صالحة
- تحقق من توفر المخزون

#### المشكلة 2: الدفع لا يسجل
**الأعراض:** مبلغ الدفع لا يقلل الرصيد
**الحلول:**
- تحقق من إعداد طريقة الدفع
- تحقق من مطابقة العملة
- تأكد من ترحيل الفاتورة
- راجع أذونات المستخدم

#### المشكلة 3: حسابات الضرائب غير صحيحة
**الأعراض:** مبالغ الضرائب تبدو خاطئة
**الحلول:**
- تحقق من أن أسعار الضرائب حديثة
- تحقق من حالة ضريبة العميل
- راجع فئات ضريبة البنود
- قم بتحديث قواعد الضرائب إذا لزم الأمر

### الحصول على المساعدة

**موارد الدعم:**
- دليل المستخدم (هذا الدليل)
- دروس الفيديو
- مكتب المساعدة: <EMAIL>
- جلسات التدريب: ندوات شهرية عبر الإنترنت

---

## مرجع سريع

### اختصارات لوحة المفاتيح

| الإجراء | الاختصار |
|--------|----------|
| فاتورة جديدة | Ctrl+N |
| حفظ الفاتورة | Ctrl+S |
| طباعة الفاتورة | Ctrl+P |
| البحث عن الفواتير | Ctrl+F |
| دفع سريع | Ctrl+Shift+P |

### المهام الشائعة

| المهمة | مسار التنقل |
|------|-----------------|
| عرض جميع الفواتير | المبيعات → الفواتير → قائمة |
| إنشاء فاتورة | المبيعات → الفواتير → إضافة |
| معالجة الدفع | الفاتورة → الإجراءات → دفع |
| إنشاء تقرير | التقارير → المبيعات → الفواتير |
| تصدير البيانات | قائمة → تصدير → اختر التنسيق |

---

## الخلاصة

يغطي هذا الدليل الشامل جميع جوانب نظام إدارة الفواتير. الممارسة المنتظمة لهذه الميزات ستضمن معالجة الفواتير بكفاءة ودقة.

**الخطوات التالية:**
1. تدرب على إنشاء فواتير اختبارية
2. استكشف الميزات المتقدمة مثل الأقساط
3. قم بإعداد التكامل مع نظام المحاسبة الخاص بك
4. جدولة تحديثات تدريب منتظمة

**تذكر:** مفتاح إتقان نظام الفواتير هو الممارسة المستمرة والبقاء على اطلاع بالميزات الجديدة وأفضل الممارسات.

---

*للحصول على دعم إضافي أو جلسات تدريب مخصصة، اتصل بفريق التدريب لدينا على <EMAIL>*